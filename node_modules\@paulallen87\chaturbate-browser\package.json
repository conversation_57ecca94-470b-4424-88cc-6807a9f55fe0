{"name": "@paulallen87/chaturbate-browser", "version": "1.2.1", "description": "Headless browser for interacting with Chaturbate's websocket", "main": "index.js", "engines": {"node": ">=8.0.0"}, "scripts": {"lint": "node_modules/.bin/eslint . --ext js --ignore-path .gitignore", "lint:fix": "node_modules/.bin/eslint --fix . --ext js --ignore-path .gitignore", "test": "node_modules/.bin/mocha --reporter spec", "cover": "node_modules/istanbul/lib/cli.js cover node_modules/mocha/bin/_mocha -- -R spec test/*"}, "repository": {"type": "git", "url": "git+https://github.com/paulallen87/chaturbate-browser.git"}, "keywords": ["chaturbate"], "author": "<PERSON><PERSON> <“<EMAIL>”>", "license": "GPL-3.0", "bugs": {"url": "https://github.com/paulallen87/chaturbate-browser/issues"}, "homepage": "https://github.com/paulallen87/chaturbate-browser#readme", "devDependencies": {"chai": "^4.0.2", "coveralls": "^3.0.2", "eslint": "^5.7.0", "eslint-config-google": "^0.11.0", "eslint-plugin-jsdoc": "^3.1.1", "eslint-plugin-node": "^7.0.1", "istanbul": "^1.1.0-alpha.1", "mocha": "^5.2.0"}, "dependencies": {"chrome-launcher": "^0.10.5", "chrome-remote-interface": "^0.26.1", "debug": "^4.1.0"}}
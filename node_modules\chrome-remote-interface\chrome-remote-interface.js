module.exports=function(e){function t(i){if(n[i])return n[i].exports;var r=n[i]={exports:{},id:i,loaded:!1};return e[i].call(r.exports,r,r.exports,t),r.loaded=!0,r.exports}var n={};return t.m=e,t.c=n,t.p="",t(0)}([function(e,t,n){(function(t){"use strict";function i(e,n){"function"==typeof e&&(n=e,e=void 0);var i=new r;return"function"==typeof n?(t.nextTick(function(){new a(e,i)}),i.once("connect",n)):new Promise(function(t,n){i.once("connect",t),i.once("error",n),new a(e,i)})}var r=n(2),o=n(3),a=n(44);e.exports=i,e.exports.Protocol=o.Protocol,e.exports.List=o.List,e.exports.New=o.New,e.exports.Activate=o.Activate,e.exports.Close=o.Close,e.exports.Version=o.Version}).call(t,n(1))},function(e,t){function n(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}function r(e){if(c===setTimeout)return setTimeout(e,0);if((c===n||!c)&&setTimeout)return c=setTimeout,setTimeout(e,0);try{return c(e,0)}catch(t){try{return c.call(null,e,0)}catch(t){return c.call(this,e,0)}}}function o(e){if(l===clearTimeout)return clearTimeout(e);if((l===i||!l)&&clearTimeout)return l=clearTimeout,clearTimeout(e);try{return l(e)}catch(t){try{return l.call(null,e)}catch(t){return l.call(this,e)}}}function a(){f&&u&&(f=!1,u.length?h=u.concat(h):y=-1,h.length&&s())}function s(){if(!f){var e=r(a);f=!0;for(var t=h.length;t;){for(u=h,h=[];++y<t;)u&&u[y].run();y=-1,t=h.length}u=null,f=!1,o(e)}}function p(e,t){this.fun=e,this.array=t}function d(){}var c,l,m=e.exports={};!function(){try{c="function"==typeof setTimeout?setTimeout:n}catch(e){c=n}try{l="function"==typeof clearTimeout?clearTimeout:i}catch(e){l=i}}();var u,h=[],f=!1,y=-1;m.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];h.push(new p(e,t)),1!==h.length||f||r(s)},p.prototype.run=function(){this.fun.apply(null,this.array)},m.title="browser",m.browser=!0,m.env={},m.argv=[],m.version="",m.versions={},m.on=d,m.addListener=d,m.once=d,m.off=d,m.removeListener=d,m.removeAllListeners=d,m.emit=d,m.prependListener=d,m.prependOnceListener=d,m.listeners=function(e){return[]},m.binding=function(e){throw new Error("process.binding is not supported")},m.cwd=function(){return"/"},m.chdir=function(e){throw new Error("process.chdir is not supported")},m.umask=function(){return 0}},function(e,t){function n(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function i(e){return"function"==typeof e}function r(e){return"number"==typeof e}function o(e){return"object"==typeof e&&null!==e}function a(e){return void 0===e}e.exports=n,n.EventEmitter=n,n.prototype._events=void 0,n.prototype._maxListeners=void 0,n.defaultMaxListeners=10,n.prototype.setMaxListeners=function(e){if(!r(e)||e<0||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},n.prototype.emit=function(e){var t,n,r,s,p,d;if(this._events||(this._events={}),"error"===e&&(!this._events.error||o(this._events.error)&&!this._events.error.length)){if(t=arguments[1],t instanceof Error)throw t;var c=new Error('Uncaught, unspecified "error" event. ('+t+")");throw c.context=t,c}if(n=this._events[e],a(n))return!1;if(i(n))switch(arguments.length){case 1:n.call(this);break;case 2:n.call(this,arguments[1]);break;case 3:n.call(this,arguments[1],arguments[2]);break;default:s=Array.prototype.slice.call(arguments,1),n.apply(this,s)}else if(o(n))for(s=Array.prototype.slice.call(arguments,1),d=n.slice(),r=d.length,p=0;p<r;p++)d[p].apply(this,s);return!0},n.prototype.addListener=function(e,t){var r;if(!i(t))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,i(t.listener)?t.listener:t),this._events[e]?o(this._events[e])?this._events[e].push(t):this._events[e]=[this._events[e],t]:this._events[e]=t,o(this._events[e])&&!this._events[e].warned&&(r=a(this._maxListeners)?n.defaultMaxListeners:this._maxListeners,r&&r>0&&this._events[e].length>r&&(this._events[e].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[e].length),"function"==typeof console.trace&&console.trace())),this},n.prototype.on=n.prototype.addListener,n.prototype.once=function(e,t){function n(){this.removeListener(e,n),r||(r=!0,t.apply(this,arguments))}if(!i(t))throw TypeError("listener must be a function");var r=!1;return n.listener=t,this.on(e,n),this},n.prototype.removeListener=function(e,t){var n,r,a,s;if(!i(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;if(n=this._events[e],a=n.length,r=-1,n===t||i(n.listener)&&n.listener===t)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,t);else if(o(n)){for(s=a;s-- >0;)if(n[s]===t||n[s].listener&&n[s].listener===t){r=s;break}if(r<0)return this;1===n.length?(n.length=0,delete this._events[e]):n.splice(r,1),this._events.removeListener&&this.emit("removeListener",e,t)}return this},n.prototype.removeAllListeners=function(e){var t,n;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[e]&&delete this._events[e],this;if(0===arguments.length){for(t in this._events)"removeListener"!==t&&this.removeAllListeners(t);return this.removeAllListeners("removeListener"),this._events={},this}if(n=this._events[e],i(n))this.removeListener(e,n);else if(n)for(;n.length;)this.removeListener(e,n[n.length-1]);return delete this._events[e],this},n.prototype.listeners=function(e){var t;return t=this._events&&this._events[e]?i(this._events[e])?[this._events[e]]:this._events[e].slice():[]},n.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(i(t))return 1;if(t)return t.length}return 0},n.listenerCount=function(e,t){return e.listenerCount(t)}},function(e,t,n){"use strict";function i(e,t){e.host=e.host||u.HOST,e.port=e.port||u.PORT,e.secure=!!e.secure,h(e.secure?m:l,e,t)}function r(e){return function(t,n){return"function"==typeof t&&(n=t,t=void 0),t=t||{},"function"==typeof n?void e(t,n):new Promise(function(n,i){e(t,function(e,t){e?i(e):n(t)})})}}function o(e,t){if(e.local){var r=n(43);return void t(null,r)}e.path="/json/protocol",i(e,function(e,n){e?t(e):t(null,JSON.parse(n))})}function a(e,t){e.path="/json/list",i(e,function(e,n){e?t(e):t(null,JSON.parse(n))})}function s(e,t){e.path="/json/new",Object.prototype.hasOwnProperty.call(e,"url")&&(e.path+="?"+e.url),i(e,function(e,n){e?t(e):t(null,JSON.parse(n))})}function p(e,t){e.path="/json/activate/"+e.id,i(e,function(e){t(e?e:null)})}function d(e,t){e.path="/json/close/"+e.id,i(e,function(e){t(e?e:null)})}function c(e,t){e.path="/json/version",i(e,function(e,n){e?t(e):t(null,JSON.parse(n))})}var l=n(4),m=n(40),u=n(41),h=n(42);e.exports.Protocol=r(o),e.exports.List=r(a),e.exports.New=r(s),e.exports.Activate=r(p),e.exports.Close=r(d),e.exports.Version=r(c)},function(e,t,n){(function(e){var i=n(5),r=n(31),o=n(32),a=n(33),s=t;s.request=function(t,n){t="string"==typeof t?a.parse(t):r(t);var o=e.location.protocol.search(/^https?:$/)===-1?"http:":"",s=t.protocol||o,p=t.hostname||t.host,d=t.port,c=t.path||"/";p&&p.indexOf(":")!==-1&&(p="["+p+"]"),t.url=(p?s+"//"+p:"")+(d?":"+d:"")+c,t.method=(t.method||"GET").toUpperCase(),t.headers=t.headers||{};var l=new i(t);return n&&l.on("response",n),l},s.get=function(e,t){var n=s.request(e,t);return n.end(),n},s.Agent=function(){},s.Agent.defaultMaxSockets=4,s.STATUS_CODES=o,s.METHODS=["CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","REPORT","SEARCH","SUBSCRIBE","TRACE","UNLOCK","UNSUBSCRIBE"]}).call(t,function(){return this}())},function(e,t,n){(function(t,i,r){function o(e,t){return s.fetch&&t?"fetch":s.mozchunkedarraybuffer?"moz-chunked-arraybuffer":s.msstream?"ms-stream":s.arraybuffer&&e?"arraybuffer":s.vbArray&&e?"text:vbarray":"text"}function a(e){try{var t=e.status;return null!==t&&0!==t}catch(e){return!1}}var s=n(10),p=n(11),d=n(12),c=n(13),l=n(30),m=d.IncomingMessage,u=d.readyStates,h=e.exports=function(e){var n=this;c.Writable.call(n),n._opts=e,n._body=[],n._headers={},e.auth&&n.setHeader("Authorization","Basic "+new t(e.auth).toString("base64")),Object.keys(e.headers).forEach(function(t){n.setHeader(t,e.headers[t])});var i,r=!0;if("disable-fetch"===e.mode||"timeout"in e)r=!1,i=!0;else if("prefer-streaming"===e.mode)i=!1;else if("allow-wrong-content-type"===e.mode)i=!s.overrideMimeType;else{if(e.mode&&"default"!==e.mode&&"prefer-fast"!==e.mode)throw new Error("Invalid value for opts.mode");i=!0}n._mode=o(i,r),n.on("finish",function(){n._onFinish()})};p(h,c.Writable),h.prototype.setHeader=function(e,t){var n=this,i=e.toLowerCase();f.indexOf(i)===-1&&(n._headers[i]={name:e,value:t})},h.prototype.getHeader=function(e){var t=this._headers[e.toLowerCase()];return t?t.value:null},h.prototype.removeHeader=function(e){var t=this;delete t._headers[e.toLowerCase()]},h.prototype._onFinish=function(){var e=this;if(!e._destroyed){var n=e._opts,o=e._headers,a=null;"GET"!==n.method&&"HEAD"!==n.method&&(a=s.blobConstructor?new i.Blob(e._body.map(function(e){return l(e)}),{type:(o["content-type"]||{}).value||""}):t.concat(e._body).toString());var p=[];if(Object.keys(o).forEach(function(e){var t=o[e].name,n=o[e].value;Array.isArray(n)?n.forEach(function(e){p.push([t,e])}):p.push([t,n])}),"fetch"===e._mode)i.fetch(e._opts.url,{method:e._opts.method,headers:p,body:a||void 0,mode:"cors",credentials:n.withCredentials?"include":"same-origin"}).then(function(t){e._fetchResponse=t,e._connect()},function(t){e.emit("error",t)});else{var d=e._xhr=new i.XMLHttpRequest;try{d.open(e._opts.method,e._opts.url,!0)}catch(t){return void r.nextTick(function(){e.emit("error",t)})}"responseType"in d&&(d.responseType=e._mode.split(":")[0]),"withCredentials"in d&&(d.withCredentials=!!n.withCredentials),"text"===e._mode&&"overrideMimeType"in d&&d.overrideMimeType("text/plain; charset=x-user-defined"),"timeout"in n&&(d.timeout=n.timeout,d.ontimeout=function(){e.emit("timeout")}),p.forEach(function(e){d.setRequestHeader(e[0],e[1])}),e._response=null,d.onreadystatechange=function(){switch(d.readyState){case u.LOADING:case u.DONE:e._onXHRProgress()}},"moz-chunked-arraybuffer"===e._mode&&(d.onprogress=function(){e._onXHRProgress()}),d.onerror=function(){e._destroyed||e.emit("error",new Error("XHR error"))};try{d.send(a)}catch(t){return void r.nextTick(function(){e.emit("error",t)})}}}},h.prototype._onXHRProgress=function(){var e=this;a(e._xhr)&&!e._destroyed&&(e._response||e._connect(),e._response._onXHRProgress())},h.prototype._connect=function(){var e=this;e._destroyed||(e._response=new m(e._xhr,e._fetchResponse,e._mode),e._response.on("error",function(t){e.emit("error",t)}),e.emit("response",e._response))},h.prototype._write=function(e,t,n){var i=this;i._body.push(e),n()},h.prototype.abort=h.prototype.destroy=function(){var e=this;e._destroyed=!0,e._response&&(e._response._destroyed=!0),e._xhr&&e._xhr.abort()},h.prototype.end=function(e,t,n){var i=this;"function"==typeof e&&(n=e,e=void 0),c.Writable.prototype.end.call(i,e,t,n)},h.prototype.flushHeaders=function(){},h.prototype.setTimeout=function(){},h.prototype.setNoDelay=function(){},h.prototype.setSocketKeepAlive=function(){};var f=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","cookie","cookie2","date","dnt","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","user-agent","via"]}).call(t,n(6).Buffer,function(){return this}(),n(1))},function(e,t,n){(function(e){"use strict";function i(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}function r(){return a.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function o(e,t){if(r()<t)throw new RangeError("Invalid typed array length");return a.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=a.prototype):(null===e&&(e=new a(t)),e.length=t),e}function a(e,t,n){if(!(a.TYPED_ARRAY_SUPPORT||this instanceof a))return new a(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return c(this,e)}return s(this,e,t,n)}function s(e,t,n,i){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?u(e,t,n,i):"string"==typeof t?l(e,t,n):h(e,t)}function p(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function d(e,t,n,i){return p(t),t<=0?o(e,t):void 0!==n?"string"==typeof i?o(e,t).fill(n,i):o(e,t).fill(n):o(e,t)}function c(e,t){if(p(t),e=o(e,t<0?0:0|f(t)),!a.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function l(e,t,n){if("string"==typeof n&&""!==n||(n="utf8"),!a.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var i=0|g(t,n);e=o(e,i);var r=e.write(t,n);return r!==i&&(e=e.slice(0,r)),e}function m(e,t){var n=t.length<0?0:0|f(t.length);e=o(e,n);for(var i=0;i<n;i+=1)e[i]=255&t[i];return e}function u(e,t,n,i){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(i||0))throw new RangeError("'length' is out of bounds");return t=void 0===n&&void 0===i?new Uint8Array(t):void 0===i?new Uint8Array(t,n):new Uint8Array(t,n,i),a.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=a.prototype):e=m(e,t),e}function h(e,t){if(a.isBuffer(t)){var n=0|f(t.length);return e=o(e,n),0===e.length?e:(t.copy(e,0,0,n),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||Y(t.length)?o(e,0):m(e,t);if("Buffer"===t.type&&Z(t.data))return m(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function f(e){if(e>=r())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+r().toString(16)+" bytes");return 0|e}function y(e){return+e!=e&&(e=0),a.alloc(+e)}function g(e,t){if(a.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return z(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return X(e).length;default:if(i)return z(e).length;t=(""+t).toLowerCase(),i=!0}}function b(e,t,n){var i=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,t>>>=0,n<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return N(this,t,n);case"utf8":case"utf-8":return D(this,t,n);case"ascii":return j(this,t,n);case"latin1":case"binary":return E(this,t,n);case"base64":return $(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return P(this,t,n);default:if(i)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),i=!0}}function v(e,t,n){var i=e[t];e[t]=e[n],e[n]=i}function w(e,t,n,i,r){if(0===e.length)return-1;if("string"==typeof n?(i=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=r?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(r)return-1;n=e.length-1}else if(n<0){if(!r)return-1;n=0}if("string"==typeof t&&(t=a.from(t,i)),a.isBuffer(t))return 0===t.length?-1:S(e,t,n,i,r);if("number"==typeof t)return t&=255,a.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?r?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):S(e,[t],n,i,r);throw new TypeError("val must be string, number or Buffer")}function S(e,t,n,i,r){function o(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}var a=1,s=e.length,p=t.length;if(void 0!==i&&(i=String(i).toLowerCase(),"ucs2"===i||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(e.length<2||t.length<2)return-1;a=2,s/=2,p/=2,n/=2}var d;if(r){var c=-1;for(d=n;d<s;d++)if(o(e,d)===o(t,c===-1?0:d-c)){if(c===-1&&(c=d),d-c+1===p)return c*a}else c!==-1&&(d-=d-c),c=-1}else for(n+p>s&&(n=s-p),d=n;d>=0;d--){for(var l=!0,m=0;m<p;m++)if(o(e,d+m)!==o(t,m)){l=!1;break}if(l)return d}return-1}function I(e,t,n,i){n=Number(n)||0;var r=e.length-n;i?(i=Number(i),i>r&&(i=r)):i=r;var o=t.length;if(o%2!==0)throw new TypeError("Invalid hex string");i>o/2&&(i=o/2);for(var a=0;a<i;++a){var s=parseInt(t.substr(2*a,2),16);if(isNaN(s))return a;e[n+a]=s}return a}function x(e,t,n,i){return J(z(t,e.length-n),e,n,i)}function T(e,t,n,i){return J(V(t),e,n,i)}function k(e,t,n,i){return T(e,t,n,i)}function R(e,t,n,i){return J(X(t),e,n,i)}function C(e,t,n,i){return J(G(t,e.length-n),e,n,i)}function $(e,t,n){return 0===t&&n===e.length?K.fromByteArray(e):K.fromByteArray(e.slice(t,n))}function D(e,t,n){n=Math.min(e.length,n);for(var i=[],r=t;r<n;){var o=e[r],a=null,s=o>239?4:o>223?3:o>191?2:1;if(r+s<=n){var p,d,c,l;switch(s){case 1:o<128&&(a=o);break;case 2:p=e[r+1],128===(192&p)&&(l=(31&o)<<6|63&p,l>127&&(a=l));break;case 3:p=e[r+1],d=e[r+2],128===(192&p)&&128===(192&d)&&(l=(15&o)<<12|(63&p)<<6|63&d,l>2047&&(l<55296||l>57343)&&(a=l));break;case 4:p=e[r+1],d=e[r+2],c=e[r+3],128===(192&p)&&128===(192&d)&&128===(192&c)&&(l=(15&o)<<18|(63&p)<<12|(63&d)<<6|63&c,l>65535&&l<1114112&&(a=l))}}null===a?(a=65533,s=1):a>65535&&(a-=65536,i.push(a>>>10&1023|55296),a=56320|1023&a),i.push(a),r+=s}return O(i)}function O(e){var t=e.length;if(t<=ee)return String.fromCharCode.apply(String,e);for(var n="",i=0;i<t;)n+=String.fromCharCode.apply(String,e.slice(i,i+=ee));return n}function j(e,t,n){var i="";n=Math.min(e.length,n);for(var r=t;r<n;++r)i+=String.fromCharCode(127&e[r]);return i}function E(e,t,n){var i="";n=Math.min(e.length,n);for(var r=t;r<n;++r)i+=String.fromCharCode(e[r]);return i}function N(e,t,n){var i=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>i)&&(n=i);for(var r="",o=t;o<n;++o)r+=W(e[o]);return r}function P(e,t,n){for(var i=e.slice(t,n),r="",o=0;o<i.length;o+=2)r+=String.fromCharCode(i[o]+256*i[o+1]);return r}function A(e,t,n){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function M(e,t,n,i,r,o){if(!a.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>r||t<o)throw new RangeError('"value" argument is out of bounds');if(n+i>e.length)throw new RangeError("Index out of range")}function L(e,t,n,i){t<0&&(t=65535+t+1);for(var r=0,o=Math.min(e.length-n,2);r<o;++r)e[n+r]=(t&255<<8*(i?r:1-r))>>>8*(i?r:1-r)}function q(e,t,n,i){t<0&&(t=4294967295+t+1);for(var r=0,o=Math.min(e.length-n,4);r<o;++r)e[n+r]=t>>>8*(i?r:3-r)&255}function _(e,t,n,i,r,o){if(n+i>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function U(e,t,n,i,r){return r||_(e,t,n,4,3.4028234663852886e38,-3.4028234663852886e38),Q.write(e,t,n,i,23,4),n+4}function F(e,t,n,i,r){return r||_(e,t,n,8,1.7976931348623157e308,-1.7976931348623157e308),Q.write(e,t,n,i,52,8),n+8}function B(e){if(e=H(e).replace(te,""),e.length<2)return"";for(;e.length%4!==0;)e+="=";return e}function H(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function W(e){return e<16?"0"+e.toString(16):e.toString(16)}function z(e,t){t=t||1/0;for(var n,i=e.length,r=null,o=[],a=0;a<i;++a){if(n=e.charCodeAt(a),n>55295&&n<57344){if(!r){if(n>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(a+1===i){(t-=3)>-1&&o.push(239,191,189);continue}r=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),r=n;continue}n=(r-55296<<10|n-56320)+65536}else r&&(t-=3)>-1&&o.push(239,191,189);if(r=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function V(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}function G(e,t){for(var n,i,r,o=[],a=0;a<e.length&&!((t-=2)<0);++a)n=e.charCodeAt(a),i=n>>8,r=n%256,o.push(r),o.push(i);return o}function X(e){return K.toByteArray(B(e))}function J(e,t,n,i){for(var r=0;r<i&&!(r+n>=t.length||r>=e.length);++r)t[r+n]=e[r];return r}function Y(e){return e!==e}var K=n(7),Q=n(8),Z=n(9);t.Buffer=a,t.SlowBuffer=y,t.INSPECT_MAX_BYTES=50,a.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:i(),t.kMaxLength=r(),a.poolSize=8192,a._augment=function(e){return e.__proto__=a.prototype,e},a.from=function(e,t,n){return s(null,e,t,n)},a.TYPED_ARRAY_SUPPORT&&(a.prototype.__proto__=Uint8Array.prototype,a.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&a[Symbol.species]===a&&Object.defineProperty(a,Symbol.species,{value:null,configurable:!0})),a.alloc=function(e,t,n){return d(null,e,t,n)},a.allocUnsafe=function(e){return c(null,e)},a.allocUnsafeSlow=function(e){return c(null,e)},a.isBuffer=function(e){return!(null==e||!e._isBuffer)},a.compare=function(e,t){if(!a.isBuffer(e)||!a.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,i=t.length,r=0,o=Math.min(n,i);r<o;++r)if(e[r]!==t[r]){n=e[r],i=t[r];break}return n<i?-1:i<n?1:0},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Z(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var i=a.allocUnsafe(t),r=0;for(n=0;n<e.length;++n){var o=e[n];if(!a.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(i,r),r+=o.length}return i},a.byteLength=g,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)v(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)v(this,t,t+3),v(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)v(this,t,t+7),v(this,t+1,t+6),v(this,t+2,t+5),v(this,t+3,t+4);return this},a.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?D(this,0,e):b.apply(this,arguments)},a.prototype.equals=function(e){if(!a.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){var e="",n=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(e+=" ... ")),"<Buffer "+e+">"},a.prototype.compare=function(e,t,n,i,r){if(!a.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===i&&(i=0),void 0===r&&(r=this.length),t<0||n>e.length||i<0||r>this.length)throw new RangeError("out of range index");if(i>=r&&t>=n)return 0;if(i>=r)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,i>>>=0,r>>>=0,this===e)return 0;for(var o=r-i,s=n-t,p=Math.min(o,s),d=this.slice(i,r),c=e.slice(t,n),l=0;l<p;++l)if(d[l]!==c[l]){o=d[l],s=c[l];break}return o<s?-1:s<o?1:0},a.prototype.includes=function(e,t,n){return this.indexOf(e,t,n)!==-1},a.prototype.indexOf=function(e,t,n){return w(this,e,t,n,!0)},a.prototype.lastIndexOf=function(e,t,n){return w(this,e,t,n,!1)},a.prototype.write=function(e,t,n,i){if(void 0===t)i="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)i=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===i&&(i="utf8")):(i=n,n=void 0)}var r=this.length-t;if((void 0===n||n>r)&&(n=r),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var o=!1;;)switch(i){case"hex":return I(this,e,t,n);case"utf8":case"utf-8":return x(this,e,t,n);case"ascii":return T(this,e,t,n);case"latin1":case"binary":return k(this,e,t,n);case"base64":return R(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return C(this,e,t,n);default:if(o)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),o=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var ee=4096;a.prototype.slice=function(e,t){var n=this.length;e=~~e,t=void 0===t?n:~~t,e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<e&&(t=e);var i;if(a.TYPED_ARRAY_SUPPORT)i=this.subarray(e,t),i.__proto__=a.prototype;else{var r=t-e;i=new a(r,void 0);for(var o=0;o<r;++o)i[o]=this[o+e]}return i},a.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||A(e,t,this.length);for(var i=this[e],r=1,o=0;++o<t&&(r*=256);)i+=this[e+o]*r;return i},a.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||A(e,t,this.length);for(var i=this[e+--t],r=1;t>0&&(r*=256);)i+=this[e+--t]*r;return i},a.prototype.readUInt8=function(e,t){return t||A(e,1,this.length),this[e]},a.prototype.readUInt16LE=function(e,t){return t||A(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUInt16BE=function(e,t){return t||A(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUInt32LE=function(e,t){return t||A(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},a.prototype.readUInt32BE=function(e,t){return t||A(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||A(e,t,this.length);for(var i=this[e],r=1,o=0;++o<t&&(r*=256);)i+=this[e+o]*r;return r*=128,i>=r&&(i-=Math.pow(2,8*t)),i},a.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||A(e,t,this.length);for(var i=t,r=1,o=this[e+--i];i>0&&(r*=256);)o+=this[e+--i]*r;return r*=128,o>=r&&(o-=Math.pow(2,8*t)),o},a.prototype.readInt8=function(e,t){return t||A(e,1,this.length),128&this[e]?(255-this[e]+1)*-1:this[e]},a.prototype.readInt16LE=function(e,t){t||A(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt16BE=function(e,t){t||A(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},a.prototype.readInt32LE=function(e,t){return t||A(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return t||A(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return t||A(e,4,this.length),Q.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return t||A(e,4,this.length),Q.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return t||A(e,8,this.length),Q.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return t||A(e,8,this.length),Q.read(this,e,!1,52,8)},a.prototype.writeUIntLE=function(e,t,n,i){if(e=+e,t|=0,n|=0,!i){var r=Math.pow(2,8*n)-1;M(this,e,t,n,r,0)}var o=1,a=0;for(this[t]=255&e;++a<n&&(o*=256);)this[t+a]=e/o&255;return t+n},a.prototype.writeUIntBE=function(e,t,n,i){if(e=+e,t|=0,n|=0,!i){var r=Math.pow(2,8*n)-1;M(this,e,t,n,r,0)}var o=n-1,a=1;for(this[t+o]=255&e;--o>=0&&(a*=256);)this[t+o]=e/a&255;return t+n},a.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||M(this,e,t,1,255,0),a.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},a.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||M(this,e,t,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):L(this,e,t,!0),t+2},a.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||M(this,e,t,2,65535,0),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):L(this,e,t,!1),t+2},a.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||M(this,e,t,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):q(this,e,t,!0),t+4},a.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||M(this,e,t,4,4294967295,0),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):q(this,e,t,!1),t+4},a.prototype.writeIntLE=function(e,t,n,i){if(e=+e,t|=0,!i){var r=Math.pow(2,8*n-1);M(this,e,t,n,r-1,-r)}var o=0,a=1,s=0;for(this[t]=255&e;++o<n&&(a*=256);)e<0&&0===s&&0!==this[t+o-1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+n},a.prototype.writeIntBE=function(e,t,n,i){if(e=+e,t|=0,!i){var r=Math.pow(2,8*n-1);M(this,e,t,n,r-1,-r)}var o=n-1,a=1,s=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===s&&0!==this[t+o+1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+n},a.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||M(this,e,t,1,127,-128),a.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||M(this,e,t,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):L(this,e,t,!0),t+2},a.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||M(this,e,t,2,32767,-32768),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):L(this,e,t,!1),t+2},a.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||M(this,e,t,4,2147483647,-2147483648),a.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):q(this,e,t,!0),t+4},a.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||M(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),a.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):q(this,e,t,!1),t+4},a.prototype.writeFloatLE=function(e,t,n){return U(this,e,t,!0,n)},a.prototype.writeFloatBE=function(e,t,n){return U(this,e,t,!1,n)},a.prototype.writeDoubleLE=function(e,t,n){return F(this,e,t,!0,n)},a.prototype.writeDoubleBE=function(e,t,n){return F(this,e,t,!1,n)},a.prototype.copy=function(e,t,n,i){if(n||(n=0),i||0===i||(i=this.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<n&&(i=n),i===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-t<i-n&&(i=e.length-t+n);var r,o=i-n;if(this===e&&n<t&&t<i)for(r=o-1;r>=0;--r)e[r+t]=this[r+n];else if(o<1e3||!a.TYPED_ARRAY_SUPPORT)for(r=0;r<o;++r)e[r+t]=this[r+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+o),t);return o},a.prototype.fill=function(e,t,n,i){if("string"==typeof e){if("string"==typeof t?(i=t,t=0,n=this.length):"string"==typeof n&&(i=n,n=this.length),1===e.length){var r=e.charCodeAt(0);r<256&&(e=r)}if(void 0!==i&&"string"!=typeof i)throw new TypeError("encoding must be a string");if("string"==typeof i&&!a.isEncoding(i))throw new TypeError("Unknown encoding: "+i)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0);var o;if("number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var s=a.isBuffer(e)?e:z(new a(e,i).toString()),p=s.length;for(o=0;o<n-t;++o)this[o+t]=s[o%p]}return this};var te=/[^+\/0-9A-Za-z-_]/g}).call(t,function(){return this}())},function(e,t){"use strict";function n(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===e[t-2]?2:"="===e[t-1]?1:0}function i(e){return 3*e.length/4-n(e)}function r(e){var t,i,r,o,a,s=e.length;o=n(e),a=new c(3*s/4-o),i=o>0?s-4:s;
var p=0;for(t=0;t<i;t+=4)r=d[e.charCodeAt(t)]<<18|d[e.charCodeAt(t+1)]<<12|d[e.charCodeAt(t+2)]<<6|d[e.charCodeAt(t+3)],a[p++]=r>>16&255,a[p++]=r>>8&255,a[p++]=255&r;return 2===o?(r=d[e.charCodeAt(t)]<<2|d[e.charCodeAt(t+1)]>>4,a[p++]=255&r):1===o&&(r=d[e.charCodeAt(t)]<<10|d[e.charCodeAt(t+1)]<<4|d[e.charCodeAt(t+2)]>>2,a[p++]=r>>8&255,a[p++]=255&r),a}function o(e){return p[e>>18&63]+p[e>>12&63]+p[e>>6&63]+p[63&e]}function a(e,t,n){for(var i,r=[],a=t;a<n;a+=3)i=(e[a]<<16)+(e[a+1]<<8)+e[a+2],r.push(o(i));return r.join("")}function s(e){for(var t,n=e.length,i=n%3,r="",o=[],s=16383,d=0,c=n-i;d<c;d+=s)o.push(a(e,d,d+s>c?c:d+s));return 1===i?(t=e[n-1],r+=p[t>>2],r+=p[t<<4&63],r+="=="):2===i&&(t=(e[n-2]<<8)+e[n-1],r+=p[t>>10],r+=p[t>>4&63],r+=p[t<<2&63],r+="="),o.push(r),o.join("")}t.byteLength=i,t.toByteArray=r,t.fromByteArray=s;for(var p=[],d=[],c="undefined"!=typeof Uint8Array?Uint8Array:Array,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",m=0,u=l.length;m<u;++m)p[m]=l[m],d[l.charCodeAt(m)]=m;d["-".charCodeAt(0)]=62,d["_".charCodeAt(0)]=63},function(e,t){t.read=function(e,t,n,i,r){var o,a,s=8*r-i-1,p=(1<<s)-1,d=p>>1,c=-7,l=n?r-1:0,m=n?-1:1,u=e[t+l];for(l+=m,o=u&(1<<-c)-1,u>>=-c,c+=s;c>0;o=256*o+e[t+l],l+=m,c-=8);for(a=o&(1<<-c)-1,o>>=-c,c+=i;c>0;a=256*a+e[t+l],l+=m,c-=8);if(0===o)o=1-d;else{if(o===p)return a?NaN:(u?-1:1)*(1/0);a+=Math.pow(2,i),o-=d}return(u?-1:1)*a*Math.pow(2,o-i)},t.write=function(e,t,n,i,r,o){var a,s,p,d=8*o-r-1,c=(1<<d)-1,l=c>>1,m=23===r?Math.pow(2,-24)-Math.pow(2,-77):0,u=i?0:o-1,h=i?1:-1,f=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=c):(a=Math.floor(Math.log(t)/Math.LN2),t*(p=Math.pow(2,-a))<1&&(a--,p*=2),t+=a+l>=1?m/p:m*Math.pow(2,1-l),t*p>=2&&(a++,p/=2),a+l>=c?(s=0,a=c):a+l>=1?(s=(t*p-1)*Math.pow(2,r),a+=l):(s=t*Math.pow(2,l-1)*Math.pow(2,r),a=0));r>=8;e[n+u]=255&s,u+=h,s/=256,r-=8);for(a=a<<r|s,d+=r;d>0;e[n+u]=255&a,u+=h,a/=256,d-=8);e[n+u-h]|=128*f}},function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t){(function(e){function n(){if(void 0!==o)return o;if(e.XMLHttpRequest){o=new e.XMLHttpRequest;try{o.open("GET",e.XDomainRequest?"/":"https://example.com")}catch(e){o=null}}else o=null;return o}function i(e){var t=n();if(!t)return!1;try{return t.responseType=e,t.responseType===e}catch(e){}return!1}function r(e){return"function"==typeof e}t.fetch=r(e.fetch)&&r(e.ReadableStream),t.blobConstructor=!1;try{new Blob([new ArrayBuffer(1)]),t.blobConstructor=!0}catch(e){}var o,a="undefined"!=typeof e.ArrayBuffer,s=a&&r(e.ArrayBuffer.prototype.slice);t.arraybuffer=t.fetch||a&&i("arraybuffer"),t.msstream=!t.fetch&&s&&i("ms-stream"),t.mozchunkedarraybuffer=!t.fetch&&a&&i("moz-chunked-arraybuffer"),t.overrideMimeType=t.fetch||!!n()&&r(n().overrideMimeType),t.vbArray=r(e.VBArray),o=null}).call(t,function(){return this}())},function(e,t){"function"==typeof Object.create?e.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(e,t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}},function(e,t,n){(function(e,i,r){var o=n(10),a=n(11),s=n(13),p=t.readyStates={UNSENT:0,OPENED:1,HEADERS_RECEIVED:2,LOADING:3,DONE:4},d=t.IncomingMessage=function(t,n,r){function a(){d.read().then(function(e){if(!p._destroyed){if(e.done)return void p.push(null);p.push(new i(e.value)),a()}}).catch(function(e){p.emit("error",e)})}var p=this;if(s.Readable.call(p),p._mode=r,p.headers={},p.rawHeaders=[],p.trailers={},p.rawTrailers=[],p.on("end",function(){e.nextTick(function(){p.emit("close")})}),"fetch"===r){p._fetchResponse=n,p.url=n.url,p.statusCode=n.status,p.statusMessage=n.statusText,n.headers.forEach(function(e,t){p.headers[t.toLowerCase()]=e,p.rawHeaders.push(t,e)});var d=n.body.getReader();a()}else{p._xhr=t,p._pos=0,p.url=t.responseURL,p.statusCode=t.status,p.statusMessage=t.statusText;var c=t.getAllResponseHeaders().split(/\r?\n/);if(c.forEach(function(e){var t=e.match(/^([^:]+):\s*(.*)/);if(t){var n=t[1].toLowerCase();"set-cookie"===n?(void 0===p.headers[n]&&(p.headers[n]=[]),p.headers[n].push(t[2])):void 0!==p.headers[n]?p.headers[n]+=", "+t[2]:p.headers[n]=t[2],p.rawHeaders.push(t[1],t[2])}}),p._charset="x-user-defined",!o.overrideMimeType){var l=p.rawHeaders["mime-type"];if(l){var m=l.match(/;\s*charset=([^;])(;|$)/);m&&(p._charset=m[1].toLowerCase())}p._charset||(p._charset="utf-8")}}};a(d,s.Readable),d.prototype._read=function(){},d.prototype._onXHRProgress=function(){var e=this,t=e._xhr,n=null;switch(e._mode){case"text:vbarray":if(t.readyState!==p.DONE)break;try{n=new r.VBArray(t.responseBody).toArray()}catch(e){}if(null!==n){e.push(new i(n));break}case"text":try{n=t.responseText}catch(t){e._mode="text:vbarray";break}if(n.length>e._pos){var o=n.substr(e._pos);if("x-user-defined"===e._charset){for(var a=new i(o.length),s=0;s<o.length;s++)a[s]=255&o.charCodeAt(s);e.push(a)}else e.push(o,e._charset);e._pos=n.length}break;case"arraybuffer":if(t.readyState!==p.DONE||!t.response)break;n=t.response,e.push(new i(new Uint8Array(n)));break;case"moz-chunked-arraybuffer":if(n=t.response,t.readyState!==p.LOADING||!n)break;e.push(new i(new Uint8Array(n)));break;case"ms-stream":if(n=t.response,t.readyState!==p.LOADING)break;var d=new r.MSStreamReader;d.onprogress=function(){d.result.byteLength>e._pos&&(e.push(new i(new Uint8Array(d.result.slice(e._pos)))),e._pos=d.result.byteLength)},d.onload=function(){e.push(null)},d.readAsArrayBuffer(n)}e._xhr.readyState===p.DONE&&"ms-stream"!==e._mode&&e.push(null)}}).call(t,n(1),n(6).Buffer,function(){return this}())},function(e,t,n){t=e.exports=n(14),t.Stream=t,t.Readable=t,t.Writable=n(23),t.Duplex=n(22),t.Transform=n(28),t.PassThrough=n(29)},function(e,t,n){(function(t,i){"use strict";function r(e){return L.from(e)}function o(e){return L.isBuffer(e)||e instanceof q}function a(e,t,n){return"function"==typeof e.prependListener?e.prependListener(t,n):void(e._events&&e._events[t]?P(e._events[t])?e._events[t].unshift(n):e._events[t]=[n,e._events[t]]:e.on(t,n))}function s(e,t){N=N||n(22),e=e||{},this.objectMode=!!e.objectMode,t instanceof N&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var i=e.highWaterMark,r=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:r,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new H,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(B||(B=n(27).StringDecoder),this.decoder=new B(e.encoding),this.encoding=e.encoding)}function p(e){return N=N||n(22),this instanceof p?(this._readableState=new s(e,this),this.readable=!0,e&&("function"==typeof e.read&&(this._read=e.read),"function"==typeof e.destroy&&(this._destroy=e.destroy)),void M.call(this)):new p(e)}function d(e,t,n,i,o){var a=e._readableState;if(null===t)a.reading=!1,f(e,a);else{var s;o||(s=l(a,t)),s?e.emit("error",s):a.objectMode||t&&t.length>0?("string"==typeof t||a.objectMode||Object.getPrototypeOf(t)===L.prototype||(t=r(t)),i?a.endEmitted?e.emit("error",new Error("stream.unshift() after end event")):c(e,a,t,!0):a.ended?e.emit("error",new Error("stream.push() after EOF")):(a.reading=!1,a.decoder&&!n?(t=a.decoder.write(t),a.objectMode||0!==t.length?c(e,a,t,!1):b(e,a)):c(e,a,t,!1))):i||(a.reading=!1)}return m(a)}function c(e,t,n,i){t.flowing&&0===t.length&&!t.sync?(e.emit("data",n),e.read(0)):(t.length+=t.objectMode?1:n.length,i?t.buffer.unshift(n):t.buffer.push(n),t.needReadable&&y(e)),b(e,t)}function l(e,t){var n;return o(t)||"string"==typeof t||void 0===t||e.objectMode||(n=new TypeError("Invalid non-string/buffer chunk")),n}function m(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}function u(e){return e>=V?e=V:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}function h(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!==e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=u(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function f(e,t){if(!t.ended){if(t.decoder){var n=t.decoder.end();n&&n.length&&(t.buffer.push(n),t.length+=t.objectMode?1:n.length)}t.ended=!0,y(e)}}function y(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(F("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?E(g,e):g(e))}function g(e){F("emit readable"),e.emit("readable"),T(e)}function b(e,t){t.readingMore||(t.readingMore=!0,E(v,e,t))}function v(e,t){for(var n=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(F("maybeReadMore read 0"),e.read(0),n!==t.length);)n=t.length;t.readingMore=!1}function w(e){return function(){var t=e._readableState;F("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&A(e,"data")&&(t.flowing=!0,T(e))}}function S(e){F("readable nexttick read 0"),e.read(0)}function I(e,t){t.resumeScheduled||(t.resumeScheduled=!0,E(x,e,t))}function x(e,t){t.reading||(F("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),T(e),t.flowing&&!t.reading&&e.read(0)}function T(e){var t=e._readableState;for(F("flow",t.flowing);t.flowing&&null!==e.read(););}function k(e,t){if(0===t.length)return null;var n;return t.objectMode?n=t.buffer.shift():!e||e>=t.length?(n=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):n=R(e,t.buffer,t.decoder),n}function R(e,t,n){var i;return e<t.head.data.length?(i=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):i=e===t.head.data.length?t.shift():n?C(e,t):$(e,t),i}function C(e,t){var n=t.head,i=1,r=n.data;for(e-=r.length;n=n.next;){var o=n.data,a=e>o.length?o.length:e;if(r+=a===o.length?o:o.slice(0,e),e-=a,0===e){a===o.length?(++i,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=o.slice(a));break}++i}return t.length-=i,r}function $(e,t){var n=L.allocUnsafe(e),i=t.head,r=1;for(i.data.copy(n),e-=i.data.length;i=i.next;){var o=i.data,a=e>o.length?o.length:e;if(o.copy(n,n.length-e,0,a),e-=a,0===e){a===o.length?(++r,i.next?t.head=i.next:t.head=t.tail=null):(t.head=i,i.data=o.slice(a));break}++r}return t.length-=r,n}function D(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,E(O,t,e))}function O(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function j(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1}var E=n(15);e.exports=p;var N,P=n(9);p.ReadableState=s;var A=(n(2).EventEmitter,function(e,t){return e.listeners(t).length}),M=n(16),L=n(17).Buffer,q=t.Uint8Array||function(){},_=n(18);_.inherits=n(11);var U=n(19),F=void 0;F=U&&U.debuglog?U.debuglog("stream"):function(){};var B,H=n(20),W=n(21);_.inherits(p,M);var z=["error","close","destroy","pause","resume"];Object.defineProperty(p.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),p.prototype.destroy=W.destroy,p.prototype._undestroy=W.undestroy,p.prototype._destroy=function(e,t){this.push(null),t(e)},p.prototype.push=function(e,t){var n,i=this._readableState;return i.objectMode?n=!0:"string"==typeof e&&(t=t||i.defaultEncoding,t!==i.encoding&&(e=L.from(e,t),t=""),n=!0),d(this,e,t,!1,n)},p.prototype.unshift=function(e){return d(this,e,null,!0,!1)},p.prototype.isPaused=function(){return this._readableState.flowing===!1},p.prototype.setEncoding=function(e){return B||(B=n(27).StringDecoder),this._readableState.decoder=new B(e),this._readableState.encoding=e,this};var V=8388608;p.prototype.read=function(e){F("read",e),e=parseInt(e,10);var t=this._readableState,n=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return F("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?D(this):y(this),null;if(e=h(e,t),0===e&&t.ended)return 0===t.length&&D(this),null;var i=t.needReadable;F("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&(i=!0,F("length less than watermark",i)),t.ended||t.reading?(i=!1,F("reading or ended",i)):i&&(F("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=h(n,t)));var r;return r=e>0?k(e,t):null,null===r?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),n!==e&&t.ended&&D(this)),null!==r&&this.emit("data",r),r},p.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))},p.prototype.pipe=function(e,t){function n(e,t){F("onunpipe"),e===m&&t&&t.hasUnpiped===!1&&(t.hasUnpiped=!0,o())}function r(){F("onend"),e.end()}function o(){F("cleanup"),e.removeListener("close",d),e.removeListener("finish",c),e.removeListener("drain",y),e.removeListener("error",p),e.removeListener("unpipe",n),m.removeListener("end",r),m.removeListener("end",l),m.removeListener("data",s),g=!0,!u.awaitDrain||e._writableState&&!e._writableState.needDrain||y()}function s(t){F("ondata"),b=!1;var n=e.write(t);!1!==n||b||((1===u.pipesCount&&u.pipes===e||u.pipesCount>1&&j(u.pipes,e)!==-1)&&!g&&(F("false write response, pause",m._readableState.awaitDrain),m._readableState.awaitDrain++,b=!0),m.pause())}function p(t){F("onerror",t),l(),e.removeListener("error",p),0===A(e,"error")&&e.emit("error",t)}function d(){e.removeListener("finish",c),l()}function c(){F("onfinish"),e.removeListener("close",d),l()}function l(){F("unpipe"),m.unpipe(e)}var m=this,u=this._readableState;switch(u.pipesCount){case 0:u.pipes=e;break;case 1:u.pipes=[u.pipes,e];break;default:u.pipes.push(e)}u.pipesCount+=1,F("pipe count=%d opts=%j",u.pipesCount,t);var h=(!t||t.end!==!1)&&e!==i.stdout&&e!==i.stderr,f=h?r:l;u.endEmitted?E(f):m.once("end",f),e.on("unpipe",n);var y=w(m);e.on("drain",y);var g=!1,b=!1;return m.on("data",s),a(e,"error",p),e.once("close",d),e.once("finish",c),e.emit("pipe",m),u.flowing||(F("pipe resume"),m.resume()),e},p.prototype.unpipe=function(e){var t=this._readableState,n={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,n),this);if(!e){var i=t.pipes,r=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<r;o++)i[o].emit("unpipe",this,n);return this}var a=j(t.pipes,e);return a===-1?this:(t.pipes.splice(a,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,n),this)},p.prototype.on=function(e,t){var n=M.prototype.on.call(this,e,t);if("data"===e)this._readableState.flowing!==!1&&this.resume();else if("readable"===e){var i=this._readableState;i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.emittedReadable=!1,i.reading?i.length&&y(this):E(S,this))}return n},p.prototype.addListener=p.prototype.on,p.prototype.resume=function(){var e=this._readableState;return e.flowing||(F("resume"),e.flowing=!0,I(this,e)),this},p.prototype.pause=function(){return F("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(F("pause"),this._readableState.flowing=!1,this.emit("pause")),this},p.prototype.wrap=function(e){var t=this._readableState,n=!1,i=this;e.on("end",function(){if(F("wrapped end"),t.decoder&&!t.ended){var e=t.decoder.end();e&&e.length&&i.push(e)}i.push(null)}),e.on("data",function(r){if(F("wrapped data"),t.decoder&&(r=t.decoder.write(r)),(!t.objectMode||null!==r&&void 0!==r)&&(t.objectMode||r&&r.length)){var o=i.push(r);o||(n=!0,e.pause())}});for(var r in e)void 0===this[r]&&"function"==typeof e[r]&&(this[r]=function(t){return function(){return e[t].apply(e,arguments)}}(r));for(var o=0;o<z.length;o++)e.on(z[o],i.emit.bind(i,z[o]));return i._read=function(t){F("wrapped _read",t),n&&(n=!1,e.resume())},i},p._fromList=k}).call(t,function(){return this}(),n(1))},function(e,t,n){(function(t){"use strict";function n(e,n,i,r){if("function"!=typeof e)throw new TypeError('"callback" argument must be a function');var o,a,s=arguments.length;switch(s){case 0:case 1:return t.nextTick(e);case 2:return t.nextTick(function(){e.call(null,n)});case 3:return t.nextTick(function(){e.call(null,n,i)});case 4:return t.nextTick(function(){e.call(null,n,i,r)});default:for(o=new Array(s-1),a=0;a<o.length;)o[a++]=arguments[a];return t.nextTick(function(){e.apply(null,o)})}}!t.version||0===t.version.indexOf("v0.")||0===t.version.indexOf("v1.")&&0!==t.version.indexOf("v1.8.")?e.exports=n:e.exports=t.nextTick}).call(t,n(1))},function(e,t,n){e.exports=n(2).EventEmitter},function(e,t,n){function i(e,t){for(var n in e)t[n]=e[n]}function r(e,t,n){return a(e,t,n)}var o=n(6),a=o.Buffer;a.from&&a.alloc&&a.allocUnsafe&&a.allocUnsafeSlow?e.exports=o:(i(o,t),t.Buffer=r),i(a,r),r.from=function(e,t,n){if("number"==typeof e)throw new TypeError("Argument must not be a number");return a(e,t,n)},r.alloc=function(e,t,n){if("number"!=typeof e)throw new TypeError("Argument must be a number");var i=a(e);return void 0!==t?"string"==typeof n?i.fill(t,n):i.fill(t):i.fill(0),i},r.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return a(e)},r.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return o.SlowBuffer(e)}},function(e,t,n){(function(e){function n(e){return Array.isArray?Array.isArray(e):"[object Array]"===y(e)}function i(e){return"boolean"==typeof e}function r(e){return null===e}function o(e){return null==e}function a(e){return"number"==typeof e}function s(e){return"string"==typeof e}function p(e){return"symbol"==typeof e}function d(e){return void 0===e}function c(e){return"[object RegExp]"===y(e)}function l(e){return"object"==typeof e&&null!==e}function m(e){return"[object Date]"===y(e)}function u(e){return"[object Error]"===y(e)||e instanceof Error}function h(e){return"function"==typeof e}function f(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||"undefined"==typeof e}function y(e){return Object.prototype.toString.call(e)}t.isArray=n,t.isBoolean=i,t.isNull=r,t.isNullOrUndefined=o,t.isNumber=a,t.isString=s,t.isSymbol=p,t.isUndefined=d,t.isRegExp=c,t.isObject=l,t.isDate=m,t.isError=u,t.isFunction=h,t.isPrimitive=f,t.isBuffer=e.isBuffer}).call(t,n(6).Buffer)},function(e,t){},function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t,n){e.copy(t,n)}var o=n(17).Buffer;e.exports=function(){function e(){i(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return"";for(var t=this.head,n=""+t.data;t=t.next;)n+=e+t.data;return n},e.prototype.concat=function(e){if(0===this.length)return o.alloc(0);if(1===this.length)return this.head.data;for(var t=o.allocUnsafe(e>>>0),n=this.head,i=0;n;)r(n.data,t,i),i+=n.data.length,n=n.next;return t},e}()},function(e,t,n){"use strict";function i(e,t){var n=this,i=this._readableState&&this._readableState.destroyed,r=this._writableState&&this._writableState.destroyed;return i||r?void(t?t(e):!e||this._writableState&&this._writableState.errorEmitted||a(o,this,e)):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),void this._destroy(e||null,function(e){!t&&e?(a(o,n,e),n._writableState&&(n._writableState.errorEmitted=!0)):t&&t(e)}))}function r(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function o(e,t){e.emit("error",t)}var a=n(15);e.exports={destroy:i,undestroy:r}},function(e,t,n){"use strict";function i(e){return this instanceof i?(d.call(this,e),c.call(this,e),e&&e.readable===!1&&(this.readable=!1),e&&e.writable===!1&&(this.writable=!1),this.allowHalfOpen=!0,e&&e.allowHalfOpen===!1&&(this.allowHalfOpen=!1),void this.once("end",r)):new i(e)}function r(){this.allowHalfOpen||this._writableState.ended||a(o,this)}function o(e){e.end()}var a=n(15),s=Object.keys||function(e){var t=[];for(var n in e)t.push(n);return t};e.exports=i;var p=n(18);p.inherits=n(11);var d=n(14),c=n(23);p.inherits(i,d);for(var l=s(c.prototype),m=0;m<l.length;m++){var u=l[m];i.prototype[u]||(i.prototype[u]=c.prototype[u])}Object.defineProperty(i.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),i.prototype._destroy=function(e,t){this.push(null),this.end(),a(t,e)}},function(e,t,n){(function(t,i,r){"use strict";function o(e){var t=this;this.next=null,this.entry=null,this.finish=function(){C(t,e)}}function a(e){return P.from(e)}function s(e){return P.isBuffer(e)||e instanceof A}function p(){}function d(e,t){D=D||n(22),e=e||{},this.objectMode=!!e.objectMode,t instanceof D&&(this.objectMode=this.objectMode||!!e.writableObjectMode);var i=e.highWaterMark,r=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:r,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var a=e.decodeStrings===!1;this.decodeStrings=!a,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){b(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new o(this)}function c(e){return D=D||n(22),L.call(c,this)||this instanceof D?(this._writableState=new d(e,this),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final)),void N.call(this)):new c(e)}function l(e,t){var n=new Error("write after end");e.emit("error",n),$(t,n)}function m(e,t,n,i){var r=!0,o=!1;return null===n?o=new TypeError("May not write null values to stream"):"string"==typeof n||void 0===n||t.objectMode||(o=new TypeError("Invalid non-string/buffer chunk")),o&&(e.emit("error",o),$(i,o),r=!1),r}function u(e,t,n){return e.objectMode||e.decodeStrings===!1||"string"!=typeof t||(t=P.from(t,n)),t}function h(e,t,n,i,r,o){if(!n){var a=u(t,i,r);i!==a&&(n=!0,r="buffer",i=a)}var s=t.objectMode?1:i.length;t.length+=s;var p=t.length<t.highWaterMark;if(p||(t.needDrain=!0),t.writing||t.corked){var d=t.lastBufferedRequest;t.lastBufferedRequest={chunk:i,encoding:r,isBuf:n,callback:o,next:null},d?d.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else f(e,t,!1,s,i,r,o);return p}function f(e,t,n,i,r,o,a){t.writelen=i,t.writecb=a,t.writing=!0,t.sync=!0,n?e._writev(r,t.onwrite):e._write(r,o,t.onwrite),t.sync=!1}function y(e,t,n,i,r){--t.pendingcb,n?($(r,i),$(k,e,t),e._writableState.errorEmitted=!0,e.emit("error",i)):(r(i),e._writableState.errorEmitted=!0,e.emit("error",i),k(e,t))}function g(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}function b(e,t){var n=e._writableState,i=n.sync,r=n.writecb;if(g(n),t)y(e,n,i,t,r);else{var o=I(n);o||n.corked||n.bufferProcessing||!n.bufferedRequest||S(e,n),i?O(v,e,n,o,r):v(e,n,o,r)}}function v(e,t,n,i){n||w(e,t),t.pendingcb--,i(),k(e,t)}function w(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}function S(e,t){t.bufferProcessing=!0;var n=t.bufferedRequest;if(e._writev&&n&&n.next){var i=t.bufferedRequestCount,r=new Array(i),a=t.corkedRequestsFree;a.entry=n;for(var s=0,p=!0;n;)r[s]=n,n.isBuf||(p=!1),n=n.next,s+=1;r.allBuffers=p,f(e,t,!0,t.length,r,"",a.finish),t.pendingcb++,t.lastBufferedRequest=null,a.next?(t.corkedRequestsFree=a.next,a.next=null):t.corkedRequestsFree=new o(t)}else{for(;n;){var d=n.chunk,c=n.encoding,l=n.callback,m=t.objectMode?1:d.length;if(f(e,t,!1,m,d,c,l),n=n.next,t.writing)break}null===n&&(t.lastBufferedRequest=null)}t.bufferedRequestCount=0,t.bufferedRequest=n,t.bufferProcessing=!1}function I(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function x(e,t){e._final(function(n){t.pendingcb--,n&&e.emit("error",n),t.prefinished=!0,e.emit("prefinish"),k(e,t)})}function T(e,t){t.prefinished||t.finalCalled||("function"==typeof e._final?(t.pendingcb++,t.finalCalled=!0,$(x,e,t)):(t.prefinished=!0,e.emit("prefinish")))}function k(e,t){var n=I(t);return n&&(T(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"))),n}function R(e,t,n){t.ending=!0,k(e,t),n&&(t.finished?$(n):e.once("finish",n)),t.ended=!0,e.writable=!1}function C(e,t,n){var i=e.entry;for(e.entry=null;i;){var r=i.callback;t.pendingcb--,r(n),i=i.next}t.corkedRequestsFree?t.corkedRequestsFree.next=e:t.corkedRequestsFree=e}var $=n(15);e.exports=c;var D,O=!t.browser&&["v0.10","v0.9."].indexOf(t.version.slice(0,5))>-1?i:$;c.WritableState=d;var j=n(18);j.inherits=n(11);var E={deprecate:n(26)},N=n(16),P=n(17).Buffer,A=r.Uint8Array||function(){},M=n(21);j.inherits(c,N),d.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(d.prototype,"buffer",{get:E.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}();var L;"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(L=Function.prototype[Symbol.hasInstance],Object.defineProperty(c,Symbol.hasInstance,{value:function(e){return!!L.call(this,e)||e&&e._writableState instanceof d}})):L=function(e){return e instanceof this},c.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},c.prototype.write=function(e,t,n){var i=this._writableState,r=!1,o=s(e)&&!i.objectMode;return o&&!P.isBuffer(e)&&(e=a(e)),"function"==typeof t&&(n=t,t=null),o?t="buffer":t||(t=i.defaultEncoding),"function"!=typeof n&&(n=p),i.ended?l(this,n):(o||m(this,i,e,n))&&(i.pendingcb++,r=h(this,i,o,e,t,n)),r},c.prototype.cork=function(){var e=this._writableState;e.corked++},c.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.finished||e.bufferProcessing||!e.bufferedRequest||S(this,e))},c.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},c.prototype._write=function(e,t,n){n(new Error("_write() is not implemented"))},c.prototype._writev=null,c.prototype.end=function(e,t,n){var i=this._writableState;"function"==typeof e?(n=e,e=null,t=null):"function"==typeof t&&(n=t,t=null),null!==e&&void 0!==e&&this.write(e,t),i.corked&&(i.corked=1,this.uncork()),i.ending||i.finished||R(this,i,n)},Object.defineProperty(c.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),c.prototype.destroy=M.destroy,c.prototype._undestroy=M.undestroy,c.prototype._destroy=function(e,t){this.end(),t(e)}}).call(t,n(1),n(24).setImmediate,function(){return this}())},function(e,t,n){function i(e,t){this._id=e,this._clearFn=t}var r=Function.prototype.apply;t.setTimeout=function(){return new i(r.call(setTimeout,window,arguments),clearTimeout)},t.setInterval=function(){return new i(r.call(setInterval,window,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},i.prototype.unref=i.prototype.ref=function(){},i.prototype.close=function(){this._clearFn.call(window,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},n(25),t.setImmediate=setImmediate,t.clearImmediate=clearImmediate},function(e,t,n){(function(e,t){!function(e,n){"use strict";function i(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var i={callback:e,args:t};return f[h]=i,u(h),h++}function r(e){delete f[e]}function o(e){var t=e.callback,i=e.args;switch(i.length){case 0:t();break;case 1:t(i[0]);break;case 2:t(i[0],i[1]);break;case 3:t(i[0],i[1],i[2]);break;default:t.apply(n,i)}}function a(e){if(y)setTimeout(a,0,e);else{var t=f[e];if(t){y=!0;try{o(t)}finally{r(e),y=!1}}}}function s(){u=function(e){t.nextTick(function(){a(e)})}}function p(){if(e.postMessage&&!e.importScripts){var t=!0,n=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=n,t}}function d(){var t="setImmediate$"+Math.random()+"$",n=function(n){n.source===e&&"string"==typeof n.data&&0===n.data.indexOf(t)&&a(+n.data.slice(t.length))};e.addEventListener?e.addEventListener("message",n,!1):e.attachEvent("onmessage",n),u=function(n){e.postMessage(t+n,"*")}}function c(){var e=new MessageChannel;e.port1.onmessage=function(e){var t=e.data;a(t)},u=function(t){e.port2.postMessage(t)}}function l(){var e=g.documentElement;u=function(t){var n=g.createElement("script");n.onreadystatechange=function(){a(t),n.onreadystatechange=null,e.removeChild(n),n=null},e.appendChild(n)}}function m(){u=function(e){setTimeout(a,0,e)}}if(!e.setImmediate){var u,h=1,f={},y=!1,g=e.document,b=Object.getPrototypeOf&&Object.getPrototypeOf(e);b=b&&b.setTimeout?b:e,"[object process]"==={}.toString.call(e.process)?s():p()?d():e.MessageChannel?c():g&&"onreadystatechange"in g.createElement("script")?l():m(),b.setImmediate=i,b.clearImmediate=r}}("undefined"==typeof self?"undefined"==typeof e?this:e:self)}).call(t,function(){return this}(),n(1))},function(e,t){(function(t){function n(e,t){function n(){if(!r){if(i("throwDeprecation"))throw new Error(t);i("traceDeprecation")?console.trace(t):console.warn(t),r=!0}return e.apply(this,arguments)}if(i("noDeprecation"))return e;var r=!1;return n}function i(e){try{if(!t.localStorage)return!1}catch(e){return!1}var n=t.localStorage[e];return null!=n&&"true"===String(n).toLowerCase()}e.exports=n}).call(t,function(){return this}())},function(e,t,n){"use strict";function i(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}function r(e){var t=i(e);if("string"!=typeof t&&(b.isEncoding===v||!v(e)))throw new Error("Unknown encoding: "+e);return t||e}
function o(e){this.encoding=r(e);var t;switch(this.encoding){case"utf16le":this.text=m,this.end=u,t=4;break;case"utf8":this.fillLast=d,t=4;break;case"base64":this.text=h,this.end=f,t=3;break;default:return this.write=y,void(this.end=g)}this.lastNeed=0,this.lastTotal=0,this.lastChar=b.allocUnsafe(t)}function a(e){return e<=127?0:e>>5===6?2:e>>4===14?3:e>>3===30?4:-1}function s(e,t,n){var i=t.length-1;if(i<n)return 0;var r=a(t[i]);return r>=0?(r>0&&(e.lastNeed=r-1),r):--i<n?0:(r=a(t[i]),r>=0?(r>0&&(e.lastNeed=r-2),r):--i<n?0:(r=a(t[i]),r>=0?(r>0&&(2===r?r=0:e.lastNeed=r-3),r):0))}function p(e,t,n){if(128!==(192&t[0]))return e.lastNeed=0,"�".repeat(n);if(e.lastNeed>1&&t.length>1){if(128!==(192&t[1]))return e.lastNeed=1,"�".repeat(n+1);if(e.lastNeed>2&&t.length>2&&128!==(192&t[2]))return e.lastNeed=2,"�".repeat(n+2)}}function d(e){var t=this.lastTotal-this.lastNeed,n=p(this,e,t);return void 0!==n?n:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function c(e,t){var n=s(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=n;var i=e.length-(n-this.lastNeed);return e.copy(this.lastChar,0,i),e.toString("utf8",t,i)}function l(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�".repeat(this.lastTotal-this.lastNeed):t}function m(e,t){if((e.length-t)%2===0){var n=e.toString("utf16le",t);if(n){var i=n.charCodeAt(n.length-1);if(i>=55296&&i<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],n.slice(0,-1)}return n}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function u(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var n=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,n)}return t}function h(e,t){var n=(e.length-t)%3;return 0===n?e.toString("base64",t):(this.lastNeed=3-n,this.lastTotal=3,1===n?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-n))}function f(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function y(e){return e.toString(this.encoding)}function g(e){return e&&e.length?this.write(e):""}var b=n(17).Buffer,v=b.isEncoding||function(e){switch(e=""+e,e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};t.StringDecoder=o,o.prototype.write=function(e){if(0===e.length)return"";var t,n;if(this.lastNeed){if(t=this.fillLast(e),void 0===t)return"";n=this.lastNeed,this.lastNeed=0}else n=0;return n<e.length?t?t+this.text(e,n):this.text(e,n):t||""},o.prototype.end=l,o.prototype.text=c,o.prototype.fillLast=function(e){return this.lastNeed<=e.length?(e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),void(this.lastNeed-=e.length))}},function(e,t,n){"use strict";function i(e){this.afterTransform=function(t,n){return r(e,t,n)},this.needTransform=!1,this.transforming=!1,this.writecb=null,this.writechunk=null,this.writeencoding=null}function r(e,t,n){var i=e._transformState;i.transforming=!1;var r=i.writecb;if(!r)return e.emit("error",new Error("write callback called multiple times"));i.writechunk=null,i.writecb=null,null!==n&&void 0!==n&&e.push(n),r(t);var o=e._readableState;o.reading=!1,(o.needReadable||o.length<o.highWaterMark)&&e._read(o.highWaterMark)}function o(e){if(!(this instanceof o))return new o(e);s.call(this,e),this._transformState=new i(this);var t=this;this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.once("prefinish",function(){"function"==typeof this._flush?this._flush(function(e,n){a(t,e,n)}):a(t)})}function a(e,t,n){if(t)return e.emit("error",t);null!==n&&void 0!==n&&e.push(n);var i=e._writableState,r=e._transformState;if(i.length)throw new Error("Calling transform done when ws.length != 0");if(r.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}e.exports=o;var s=n(22),p=n(18);p.inherits=n(11),p.inherits(o,s),o.prototype.push=function(e,t){return this._transformState.needTransform=!1,s.prototype.push.call(this,e,t)},o.prototype._transform=function(e,t,n){throw new Error("_transform() is not implemented")},o.prototype._write=function(e,t,n){var i=this._transformState;if(i.writecb=n,i.writechunk=e,i.writeencoding=t,!i.transforming){var r=this._readableState;(i.needTransform||r.needReadable||r.length<r.highWaterMark)&&this._read(r.highWaterMark)}},o.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},o.prototype._destroy=function(e,t){var n=this;s.prototype._destroy.call(this,e,function(e){t(e),n.emit("close")})}},function(e,t,n){"use strict";function i(e){return this instanceof i?void r.call(this,e):new i(e)}e.exports=i;var r=n(28),o=n(18);o.inherits=n(11),o.inherits(i,r),i.prototype._transform=function(e,t,n){n(null,e)}},function(e,t,n){var i=n(6).Buffer;e.exports=function(e){if(e instanceof Uint8Array){if(0===e.byteOffset&&e.byteLength===e.buffer.byteLength)return e.buffer;if("function"==typeof e.buffer.slice)return e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength)}if(i.isBuffer(e)){for(var t=new Uint8Array(e.length),n=e.length,r=0;r<n;r++)t[r]=e[r];return t.buffer}throw new Error("Argument must be a Buffer")}},function(e,t){function n(){for(var e={},t=0;t<arguments.length;t++){var n=arguments[t];for(var r in n)i.call(n,r)&&(e[r]=n[r])}return e}e.exports=n;var i=Object.prototype.hasOwnProperty},function(e,t){e.exports={100:"Continue",101:"Switching Protocols",102:"Processing",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Entity",423:"Locked",424:"Failed Dependency",425:"Unordered Collection",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",509:"Bandwidth Limit Exceeded",510:"Not Extended",511:"Network Authentication Required"}},function(e,t,n){"use strict";function i(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}function r(e,t,n){if(e&&d.isObject(e)&&e instanceof i)return e;var r=new i;return r.parse(e,t,n),r}function o(e){return d.isString(e)&&(e=r(e)),e instanceof i?e.format():i.prototype.format.call(e)}function a(e,t){return r(e,!1,!0).resolve(t)}function s(e,t){return e?r(e,!1,!0).resolveObject(t):t}var p=n(34),d=n(36);t.parse=r,t.resolve=a,t.resolveObject=s,t.format=o,t.Url=i;var c=/^([a-z0-9.+-]+:)/i,l=/:[0-9]*$/,m=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,u=["<",">",'"',"`"," ","\r","\n","\t"],h=["{","}","|","\\","^","`"].concat(u),f=["'"].concat(h),y=["%","/","?",";","#"].concat(f),g=["/","?","#"],b=255,v=/^[+a-z0-9A-Z_-]{0,63}$/,w=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,S={javascript:!0,"javascript:":!0},I={javascript:!0,"javascript:":!0},x={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},T=n(37);i.prototype.parse=function(e,t,n){if(!d.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var i=e.indexOf("?"),r=i!==-1&&i<e.indexOf("#")?"?":"#",o=e.split(r),a=/\\/g;o[0]=o[0].replace(a,"/"),e=o.join(r);var s=e;if(s=s.trim(),!n&&1===e.split("#").length){var l=m.exec(s);if(l)return this.path=s,this.href=s,this.pathname=l[1],l[2]?(this.search=l[2],t?this.query=T.parse(this.search.substr(1)):this.query=this.search.substr(1)):t&&(this.search="",this.query={}),this}var u=c.exec(s);if(u){u=u[0];var h=u.toLowerCase();this.protocol=h,s=s.substr(u.length)}if(n||u||s.match(/^\/\/[^@\/]+@[^@\/]+/)){var k="//"===s.substr(0,2);!k||u&&I[u]||(s=s.substr(2),this.slashes=!0)}if(!I[u]&&(k||u&&!x[u])){for(var R=-1,C=0;C<g.length;C++){var $=s.indexOf(g[C]);$!==-1&&(R===-1||$<R)&&(R=$)}var D,O;O=R===-1?s.lastIndexOf("@"):s.lastIndexOf("@",R),O!==-1&&(D=s.slice(0,O),s=s.slice(O+1),this.auth=decodeURIComponent(D)),R=-1;for(var C=0;C<y.length;C++){var $=s.indexOf(y[C]);$!==-1&&(R===-1||$<R)&&(R=$)}R===-1&&(R=s.length),this.host=s.slice(0,R),s=s.slice(R),this.parseHost(),this.hostname=this.hostname||"";var j="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!j)for(var E=this.hostname.split(/\./),C=0,N=E.length;C<N;C++){var P=E[C];if(P&&!P.match(v)){for(var A="",M=0,L=P.length;M<L;M++)A+=P.charCodeAt(M)>127?"x":P[M];if(!A.match(v)){var q=E.slice(0,C),_=E.slice(C+1),U=P.match(w);U&&(q.push(U[1]),_.unshift(U[2])),_.length&&(s="/"+_.join(".")+s),this.hostname=q.join(".");break}}}this.hostname.length>b?this.hostname="":this.hostname=this.hostname.toLowerCase(),j||(this.hostname=p.toASCII(this.hostname));var F=this.port?":"+this.port:"",B=this.hostname||"";this.host=B+F,this.href+=this.host,j&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==s[0]&&(s="/"+s))}if(!S[h])for(var C=0,N=f.length;C<N;C++){var H=f[C];if(s.indexOf(H)!==-1){var W=encodeURIComponent(H);W===H&&(W=escape(H)),s=s.split(H).join(W)}}var z=s.indexOf("#");z!==-1&&(this.hash=s.substr(z),s=s.slice(0,z));var V=s.indexOf("?");if(V!==-1?(this.search=s.substr(V),this.query=s.substr(V+1),t&&(this.query=T.parse(this.query)),s=s.slice(0,V)):t&&(this.search="",this.query={}),s&&(this.pathname=s),x[h]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){var F=this.pathname||"",G=this.search||"";this.path=F+G}return this.href=this.format(),this},i.prototype.format=function(){var e=this.auth||"";e&&(e=encodeURIComponent(e),e=e.replace(/%3A/i,":"),e+="@");var t=this.protocol||"",n=this.pathname||"",i=this.hash||"",r=!1,o="";this.host?r=e+this.host:this.hostname&&(r=e+(this.hostname.indexOf(":")===-1?this.hostname:"["+this.hostname+"]"),this.port&&(r+=":"+this.port)),this.query&&d.isObject(this.query)&&Object.keys(this.query).length&&(o=T.stringify(this.query));var a=this.search||o&&"?"+o||"";return t&&":"!==t.substr(-1)&&(t+=":"),this.slashes||(!t||x[t])&&r!==!1?(r="//"+(r||""),n&&"/"!==n.charAt(0)&&(n="/"+n)):r||(r=""),i&&"#"!==i.charAt(0)&&(i="#"+i),a&&"?"!==a.charAt(0)&&(a="?"+a),n=n.replace(/[?#]/g,function(e){return encodeURIComponent(e)}),a=a.replace("#","%23"),t+r+n+a+i},i.prototype.resolve=function(e){return this.resolveObject(r(e,!1,!0)).format()},i.prototype.resolveObject=function(e){if(d.isString(e)){var t=new i;t.parse(e,!1,!0),e=t}for(var n=new i,r=Object.keys(this),o=0;o<r.length;o++){var a=r[o];n[a]=this[a]}if(n.hash=e.hash,""===e.href)return n.href=n.format(),n;if(e.slashes&&!e.protocol){for(var s=Object.keys(e),p=0;p<s.length;p++){var c=s[p];"protocol"!==c&&(n[c]=e[c])}return x[n.protocol]&&n.hostname&&!n.pathname&&(n.path=n.pathname="/"),n.href=n.format(),n}if(e.protocol&&e.protocol!==n.protocol){if(!x[e.protocol]){for(var l=Object.keys(e),m=0;m<l.length;m++){var u=l[m];n[u]=e[u]}return n.href=n.format(),n}if(n.protocol=e.protocol,e.host||I[e.protocol])n.pathname=e.pathname;else{for(var h=(e.pathname||"").split("/");h.length&&!(e.host=h.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==h[0]&&h.unshift(""),h.length<2&&h.unshift(""),n.pathname=h.join("/")}if(n.search=e.search,n.query=e.query,n.host=e.host||"",n.auth=e.auth,n.hostname=e.hostname||e.host,n.port=e.port,n.pathname||n.search){var f=n.pathname||"",y=n.search||"";n.path=f+y}return n.slashes=n.slashes||e.slashes,n.href=n.format(),n}var g=n.pathname&&"/"===n.pathname.charAt(0),b=e.host||e.pathname&&"/"===e.pathname.charAt(0),v=b||g||n.host&&e.pathname,w=v,S=n.pathname&&n.pathname.split("/")||[],h=e.pathname&&e.pathname.split("/")||[],T=n.protocol&&!x[n.protocol];if(T&&(n.hostname="",n.port=null,n.host&&(""===S[0]?S[0]=n.host:S.unshift(n.host)),n.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===h[0]?h[0]=e.host:h.unshift(e.host)),e.host=null),v=v&&(""===h[0]||""===S[0])),b)n.host=e.host||""===e.host?e.host:n.host,n.hostname=e.hostname||""===e.hostname?e.hostname:n.hostname,n.search=e.search,n.query=e.query,S=h;else if(h.length)S||(S=[]),S.pop(),S=S.concat(h),n.search=e.search,n.query=e.query;else if(!d.isNullOrUndefined(e.search)){if(T){n.hostname=n.host=S.shift();var k=!!(n.host&&n.host.indexOf("@")>0)&&n.host.split("@");k&&(n.auth=k.shift(),n.host=n.hostname=k.shift())}return n.search=e.search,n.query=e.query,d.isNull(n.pathname)&&d.isNull(n.search)||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.href=n.format(),n}if(!S.length)return n.pathname=null,n.search?n.path="/"+n.search:n.path=null,n.href=n.format(),n;for(var R=S.slice(-1)[0],C=(n.host||e.host||S.length>1)&&("."===R||".."===R)||""===R,$=0,D=S.length;D>=0;D--)R=S[D],"."===R?S.splice(D,1):".."===R?(S.splice(D,1),$++):$&&(S.splice(D,1),$--);if(!v&&!w)for(;$--;$)S.unshift("..");!v||""===S[0]||S[0]&&"/"===S[0].charAt(0)||S.unshift(""),C&&"/"!==S.join("/").substr(-1)&&S.push("");var O=""===S[0]||S[0]&&"/"===S[0].charAt(0);if(T){n.hostname=n.host=O?"":S.length?S.shift():"";var k=!!(n.host&&n.host.indexOf("@")>0)&&n.host.split("@");k&&(n.auth=k.shift(),n.host=n.hostname=k.shift())}return v=v||n.host&&S.length,v&&!O&&S.unshift(""),S.length?n.pathname=S.join("/"):(n.pathname=null,n.path=null),d.isNull(n.pathname)&&d.isNull(n.search)||(n.path=(n.pathname?n.pathname:"")+(n.search?n.search:"")),n.auth=e.auth||n.auth,n.slashes=n.slashes||e.slashes,n.href=n.format(),n},i.prototype.parseHost=function(){var e=this.host,t=l.exec(e);t&&(t=t[0],":"!==t&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)}},function(e,t,n){var i;(function(e,r){!function(o){function a(e){throw RangeError(E[e])}function s(e,t){for(var n=e.length,i=[];n--;)i[n]=t(e[n]);return i}function p(e,t){var n=e.split("@"),i="";n.length>1&&(i=n[0]+"@",e=n[1]),e=e.replace(j,".");var r=e.split("."),o=s(r,t).join(".");return i+o}function d(e){for(var t,n,i=[],r=0,o=e.length;r<o;)t=e.charCodeAt(r++),t>=55296&&t<=56319&&r<o?(n=e.charCodeAt(r++),56320==(64512&n)?i.push(((1023&t)<<10)+(1023&n)+65536):(i.push(t),r--)):i.push(t);return i}function c(e){return s(e,function(e){var t="";return e>65535&&(e-=65536,t+=A(e>>>10&1023|55296),e=56320|1023&e),t+=A(e)}).join("")}function l(e){return e-48<10?e-22:e-65<26?e-65:e-97<26?e-97:S}function m(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function u(e,t,n){var i=0;for(e=n?P(e/k):e>>1,e+=P(e/t);e>N*x>>1;i+=S)e=P(e/N);return P(i+(N+1)*e/(e+T))}function h(e){var t,n,i,r,o,s,p,d,m,h,f=[],y=e.length,g=0,b=C,v=R;for(n=e.lastIndexOf($),n<0&&(n=0),i=0;i<n;++i)e.charCodeAt(i)>=128&&a("not-basic"),f.push(e.charCodeAt(i));for(r=n>0?n+1:0;r<y;){for(o=g,s=1,p=S;r>=y&&a("invalid-input"),d=l(e.charCodeAt(r++)),(d>=S||d>P((w-g)/s))&&a("overflow"),g+=d*s,m=p<=v?I:p>=v+x?x:p-v,!(d<m);p+=S)h=S-m,s>P(w/h)&&a("overflow"),s*=h;t=f.length+1,v=u(g-o,t,0==o),P(g/t)>w-b&&a("overflow"),b+=P(g/t),g%=t,f.splice(g++,0,b)}return c(f)}function f(e){var t,n,i,r,o,s,p,c,l,h,f,y,g,b,v,T=[];for(e=d(e),y=e.length,t=C,n=0,o=R,s=0;s<y;++s)f=e[s],f<128&&T.push(A(f));for(i=r=T.length,r&&T.push($);i<y;){for(p=w,s=0;s<y;++s)f=e[s],f>=t&&f<p&&(p=f);for(g=i+1,p-t>P((w-n)/g)&&a("overflow"),n+=(p-t)*g,t=p,s=0;s<y;++s)if(f=e[s],f<t&&++n>w&&a("overflow"),f==t){for(c=n,l=S;h=l<=o?I:l>=o+x?x:l-o,!(c<h);l+=S)v=c-h,b=S-h,T.push(A(m(h+v%b,0))),c=P(v/b);T.push(A(m(c,0))),o=u(n,g,i==r),n=0,++i}++n,++t}return T.join("")}function y(e){return p(e,function(e){return D.test(e)?h(e.slice(4).toLowerCase()):e})}function g(e){return p(e,function(e){return O.test(e)?"xn--"+f(e):e})}var b=("object"==typeof t&&t&&!t.nodeType&&t,"object"==typeof e&&e&&!e.nodeType&&e,"object"==typeof r&&r);b.global!==b&&b.window!==b&&b.self!==b||(o=b);var v,w=2147483647,S=36,I=1,x=26,T=38,k=700,R=72,C=128,$="-",D=/^xn--/,O=/[^\x20-\x7E]/,j=/[\x2E\u3002\uFF0E\uFF61]/g,E={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},N=S-I,P=Math.floor,A=String.fromCharCode;v={version:"1.3.2",ucs2:{decode:d,encode:c},decode:h,encode:f,toASCII:g,toUnicode:y},i=function(){return v}.call(t,n,t,e),!(void 0!==i&&(e.exports=i))}(this)}).call(t,n(35)(e),function(){return this}())},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children=[],e.webpackPolyfill=1),e}},function(e,t){"use strict";e.exports={isString:function(e){return"string"==typeof e},isObject:function(e){return"object"==typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},function(e,t,n){"use strict";t.decode=t.parse=n(38),t.encode=t.stringify=n(39)},function(e,t){"use strict";function n(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.exports=function(e,t,i,r){t=t||"&",i=i||"=";var o={};if("string"!=typeof e||0===e.length)return o;var a=/\+/g;e=e.split(t);var s=1e3;r&&"number"==typeof r.maxKeys&&(s=r.maxKeys);var p=e.length;s>0&&p>s&&(p=s);for(var d=0;d<p;++d){var c,l,m,u,h=e[d].replace(a,"%20"),f=h.indexOf(i);f>=0?(c=h.substr(0,f),l=h.substr(f+1)):(c=h,l=""),m=decodeURIComponent(c),u=decodeURIComponent(l),n(o,m)?Array.isArray(o[m])?o[m].push(u):o[m]=[o[m],u]:o[m]=u}return o}},function(e,t){"use strict";var n=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};e.exports=function(e,t,i,r){return t=t||"&",i=i||"=",null===e&&(e=void 0),"object"==typeof e?Object.keys(e).map(function(r){var o=encodeURIComponent(n(r))+i;return Array.isArray(e[r])?e[r].map(function(e){return o+encodeURIComponent(n(e))}).join(t):o+encodeURIComponent(n(e[r]))}).join(t):r?encodeURIComponent(n(r))+i+encodeURIComponent(n(e)):""}},function(e,t,n){var i=n(4),r=e.exports;for(var o in i)i.hasOwnProperty(o)&&(r[o]=i[o]);r.request=function(e,t){return e||(e={}),e.scheme="https",e.protocol="https:",i.request.call(this,e,t)}},function(e,t){"use strict";e.exports.HOST="localhost",e.exports.PORT=9222},function(e,t){e.exports=function(e,t,n){window.criRequest(t,n)}},function(e,t){e.exports={version:{major:"1",minor:"3"},domains:[{domain:"Accessibility",experimental:!0,dependencies:["DOM"],types:[{id:"AXNodeId",description:"Unique accessibility node identifier.",type:"string"},{id:"AXValueType",description:"Enum of possible property types.",type:"string",enum:["boolean","tristate","booleanOrUndefined","idref","idrefList","integer","node","nodeList","number","string","computedString","token","tokenList","domRelation","role","internalRole","valueUndefined"]},{id:"AXValueSourceType",description:"Enum of possible property sources.",type:"string",enum:["attribute","implicit","style","contents","placeholder","relatedElement"]},{id:"AXValueNativeSourceType",description:"Enum of possible native property sources (as a subtype of a particular AXValueSourceType).",type:"string",enum:["figcaption","label","labelfor","labelwrapped","legend","tablecaption","title","other"]},{id:"AXValueSource",description:"A single source for a computed AX property.",type:"object",properties:[{name:"type",description:"What type of source this is.",$ref:"AXValueSourceType"},{name:"value",description:"The value of this property source.",optional:!0,$ref:"AXValue"},{name:"attribute",description:"The name of the relevant attribute, if any.",optional:!0,type:"string"},{name:"attributeValue",description:"The value of the relevant attribute, if any.",optional:!0,$ref:"AXValue"},{name:"superseded",description:"Whether this source is superseded by a higher priority source.",optional:!0,type:"boolean"},{name:"nativeSource",description:"The native markup source for this value, e.g. a <label> element.",optional:!0,$ref:"AXValueNativeSourceType"},{name:"nativeSourceValue",description:"The value, such as a node or node list, of the native source.",optional:!0,$ref:"AXValue"},{name:"invalid",description:"Whether the value for this property is invalid.",optional:!0,type:"boolean"},{name:"invalidReason",description:"Reason for the value being invalid, if it is.",optional:!0,type:"string"}]},{id:"AXRelatedNode",type:"object",properties:[{name:"backendDOMNodeId",description:"The BackendNodeId of the related DOM node.",$ref:"DOM.BackendNodeId"},{name:"idref",description:"The IDRef value provided, if any.",optional:!0,type:"string"},{name:"text",description:"The text alternative of this node in the current context.",optional:!0,type:"string"}]},{id:"AXProperty",type:"object",properties:[{name:"name",description:"The name of this property.",$ref:"AXPropertyName"},{name:"value",description:"The value of this property.",$ref:"AXValue"}]},{id:"AXValue",description:"A single computed AX property.",type:"object",properties:[{name:"type",description:"The type of this value.",$ref:"AXValueType"},{name:"value",description:"The computed value of this property.",optional:!0,type:"any"},{name:"relatedNodes",description:"One or more related nodes, if applicable.",optional:!0,type:"array",items:{$ref:"AXRelatedNode"}},{name:"sources",description:"The sources which contributed to the computation of this property.",optional:!0,type:"array",items:{$ref:"AXValueSource"}}]},{id:"AXPropertyName",description:"Values of AXProperty name: from 'busy' to 'roledescription' - states which apply to every AX\nnode, from 'live' to 'root' - attributes which apply to nodes in live regions, from\n'autocomplete' to 'valuetext' - attributes which apply to widgets, from 'checked' to 'selected'\n- states which apply to widgets, from 'activedescendant' to 'owns' - relationships between\nelements other than parent/child/sibling.",type:"string",enum:["busy","disabled","hidden","hiddenRoot","invalid","keyshortcuts","roledescription","live","atomic","relevant","root","autocomplete","hasPopup","level","multiselectable","orientation","multiline","readonly","required","valuemin","valuemax","valuetext","checked","expanded","modal","pressed","selected","activedescendant","controls","describedby","details","errormessage","flowto","labelledby","owns"]},{id:"AXNode",description:"A node in the accessibility tree.",type:"object",properties:[{name:"nodeId",description:"Unique identifier for this node.",$ref:"AXNodeId"},{name:"ignored",description:"Whether this node is ignored for accessibility",type:"boolean"},{name:"ignoredReasons",description:"Collection of reasons why this node is hidden.",optional:!0,type:"array",items:{$ref:"AXProperty"}},{name:"role",description:"This `Node`'s role, whether explicit or implicit.",optional:!0,$ref:"AXValue"},{name:"name",description:"The accessible name for this `Node`.",optional:!0,$ref:"AXValue"},{name:"description",description:"The accessible description for this `Node`.",optional:!0,$ref:"AXValue"},{name:"value",description:"The value for this `Node`.",optional:!0,$ref:"AXValue"},{name:"properties",description:"All other properties",optional:!0,type:"array",items:{$ref:"AXProperty"}},{name:"childIds",description:"IDs for each of this node's child nodes.",optional:!0,type:"array",items:{$ref:"AXNodeId"}},{name:"backendDOMNodeId",description:"The backend ID for the associated DOM node, if any.",optional:!0,$ref:"DOM.BackendNodeId"}]}],commands:[{name:"getPartialAXTree",description:"Fetches the accessibility node and partial accessibility tree for this DOM node, if it exists.",experimental:!0,parameters:[{name:"nodeId",description:"Identifier of the node to get the partial accessibility tree for.",optional:!0,$ref:"DOM.NodeId"},{name:"backendNodeId",description:"Identifier of the backend node to get the partial accessibility tree for.",optional:!0,$ref:"DOM.BackendNodeId"},{name:"objectId",description:"JavaScript object id of the node wrapper to get the partial accessibility tree for.",optional:!0,$ref:"Runtime.RemoteObjectId"},{name:"fetchRelatives",description:"Whether to fetch this nodes ancestors, siblings and children. Defaults to true.",optional:!0,type:"boolean"}],returns:[{name:"nodes",description:"The `Accessibility.AXNode` for this DOM node, if it exists, plus its ancestors, siblings and\nchildren, if requested.",type:"array",items:{$ref:"AXNode"}}]}]},{domain:"Animation",experimental:!0,dependencies:["Runtime","DOM"],types:[{id:"Animation",description:"Animation instance.",type:"object",properties:[{name:"id",description:"`Animation`'s id.",type:"string"},{name:"name",description:"`Animation`'s name.",type:"string"},{name:"pausedState",description:"`Animation`'s internal paused state.",type:"boolean"},{name:"playState",description:"`Animation`'s play state.",type:"string"},{name:"playbackRate",description:"`Animation`'s playback rate.",type:"number"},{name:"startTime",description:"`Animation`'s start time.",type:"number"},{name:"currentTime",description:"`Animation`'s current time.",type:"number"},{name:"type",description:"Animation type of `Animation`.",type:"string",enum:["CSSTransition","CSSAnimation","WebAnimation"]},{name:"source",description:"`Animation`'s source animation node.",optional:!0,$ref:"AnimationEffect"},{name:"cssId",description:"A unique ID for `Animation` representing the sources that triggered this CSS\nanimation/transition.",optional:!0,type:"string"}]},{id:"AnimationEffect",description:"AnimationEffect instance",type:"object",properties:[{name:"delay",description:"`AnimationEffect`'s delay.",type:"number"},{name:"endDelay",description:"`AnimationEffect`'s end delay.",type:"number"},{name:"iterationStart",description:"`AnimationEffect`'s iteration start.",type:"number"},{name:"iterations",description:"`AnimationEffect`'s iterations.",type:"number"},{name:"duration",description:"`AnimationEffect`'s iteration duration.",type:"number"},{name:"direction",description:"`AnimationEffect`'s playback direction.",type:"string"},{name:"fill",description:"`AnimationEffect`'s fill mode.",type:"string"},{name:"backendNodeId",description:"`AnimationEffect`'s target node.",optional:!0,$ref:"DOM.BackendNodeId"},{name:"keyframesRule",description:"`AnimationEffect`'s keyframes.",optional:!0,$ref:"KeyframesRule"},{name:"easing",description:"`AnimationEffect`'s timing function.",type:"string"}]},{id:"KeyframesRule",description:"Keyframes Rule",type:"object",properties:[{name:"name",description:"CSS keyframed animation's name.",optional:!0,type:"string"},{name:"keyframes",description:"List of animation keyframes.",type:"array",items:{$ref:"KeyframeStyle"}}]},{id:"KeyframeStyle",description:"Keyframe Style",type:"object",properties:[{name:"offset",description:"Keyframe's time offset.",type:"string"},{name:"easing",description:"`AnimationEffect`'s timing function.",type:"string"}]}],commands:[{name:"disable",description:"Disables animation domain notifications."},{name:"enable",description:"Enables animation domain notifications."},{name:"getCurrentTime",description:"Returns the current time of the an animation.",parameters:[{name:"id",description:"Id of animation.",type:"string"}],returns:[{name:"currentTime",description:"Current time of the page.",type:"number"}]},{name:"getPlaybackRate",description:"Gets the playback rate of the document timeline.",returns:[{name:"playbackRate",description:"Playback rate for animations on page.",type:"number"}]},{name:"releaseAnimations",description:"Releases a set of animations to no longer be manipulated.",parameters:[{name:"animations",description:"List of animation ids to seek.",type:"array",items:{type:"string"}}]},{name:"resolveAnimation",description:"Gets the remote object of the Animation.",parameters:[{name:"animationId",description:"Animation id.",type:"string"}],returns:[{name:"remoteObject",description:"Corresponding remote object.",$ref:"Runtime.RemoteObject"}]},{name:"seekAnimations",description:"Seek a set of animations to a particular time within each animation.",parameters:[{name:"animations",description:"List of animation ids to seek.",type:"array",items:{type:"string"}},{name:"currentTime",description:"Set the current time of each animation.",type:"number"}]},{name:"setPaused",description:"Sets the paused state of a set of animations.",parameters:[{name:"animations",description:"Animations to set the pause state of.",type:"array",items:{type:"string"}},{name:"paused",description:"Paused state to set to.",type:"boolean"}]},{name:"setPlaybackRate",description:"Sets the playback rate of the document timeline.",parameters:[{name:"playbackRate",description:"Playback rate for animations on page",type:"number"}]},{name:"setTiming",description:"Sets the timing of an animation node.",parameters:[{name:"animationId",description:"Animation id.",type:"string"},{name:"duration",description:"Duration of the animation.",type:"number"},{name:"delay",description:"Delay of the animation.",type:"number"}]}],events:[{name:"animationCanceled",description:"Event for when an animation has been cancelled.",parameters:[{name:"id",description:"Id of the animation that was cancelled.",type:"string"}]},{name:"animationCreated",description:"Event for each animation that has been created.",parameters:[{name:"id",description:"Id of the animation that was created.",type:"string"}]},{name:"animationStarted",description:"Event for animation that has been started.",parameters:[{name:"animation",description:"Animation that was started.",$ref:"Animation"}]}]},{domain:"ApplicationCache",experimental:!0,types:[{id:"ApplicationCacheResource",description:"Detailed application cache resource information.",type:"object",properties:[{name:"url",description:"Resource url.",type:"string"},{name:"size",description:"Resource size.",type:"integer"},{name:"type",description:"Resource type.",type:"string"}]},{id:"ApplicationCache",description:"Detailed application cache information.",type:"object",properties:[{name:"manifestURL",description:"Manifest URL.",type:"string"},{name:"size",description:"Application cache size.",type:"number"},{name:"creationTime",description:"Application cache creation time.",type:"number"},{name:"updateTime",description:"Application cache update time.",type:"number"},{name:"resources",description:"Application cache resources.",type:"array",items:{$ref:"ApplicationCacheResource"}}]},{id:"FrameWithManifest",description:"Frame identifier - manifest URL pair.",type:"object",properties:[{name:"frameId",description:"Frame identifier.",$ref:"Page.FrameId"},{name:"manifestURL",description:"Manifest URL.",type:"string"},{name:"status",description:"Application cache status.",type:"integer"}]}],commands:[{name:"enable",description:"Enables application cache domain notifications."},{name:"getApplicationCacheForFrame",description:"Returns relevant application cache data for the document in given frame.",
parameters:[{name:"frameId",description:"Identifier of the frame containing document whose application cache is retrieved.",$ref:"Page.FrameId"}],returns:[{name:"applicationCache",description:"Relevant application cache data for the document in given frame.",$ref:"ApplicationCache"}]},{name:"getFramesWithManifests",description:"Returns array of frame identifiers with manifest urls for each frame containing a document\nassociated with some application cache.",returns:[{name:"frameIds",description:"Array of frame identifiers with manifest urls for each frame containing a document\nassociated with some application cache.",type:"array",items:{$ref:"FrameWithManifest"}}]},{name:"getManifestForFrame",description:"Returns manifest URL for document in the given frame.",parameters:[{name:"frameId",description:"Identifier of the frame containing document whose manifest is retrieved.",$ref:"Page.FrameId"}],returns:[{name:"manifestURL",description:"Manifest URL for document in the given frame.",type:"string"}]}],events:[{name:"applicationCacheStatusUpdated",parameters:[{name:"frameId",description:"Identifier of the frame containing document whose application cache updated status.",$ref:"Page.FrameId"},{name:"manifestURL",description:"Manifest URL.",type:"string"},{name:"status",description:"Updated application cache status.",type:"integer"}]},{name:"networkStateUpdated",parameters:[{name:"isNowOnline",type:"boolean"}]}]},{domain:"Audits",description:"Audits domain allows investigation of page violations and possible improvements.",experimental:!0,dependencies:["Network"],commands:[{name:"getEncodedResponse",description:"Returns the response body and size if it were re-encoded with the specified settings. Only\napplies to images.",parameters:[{name:"requestId",description:"Identifier of the network request to get content for.",$ref:"Network.RequestId"},{name:"encoding",description:"The encoding to use.",type:"string",enum:["webp","jpeg","png"]},{name:"quality",description:"The quality of the encoding (0-1). (defaults to 1)",optional:!0,type:"number"},{name:"sizeOnly",description:"Whether to only return the size information (defaults to false).",optional:!0,type:"boolean"}],returns:[{name:"body",description:"The encoded body as a base64 string. Omitted if sizeOnly is true.",optional:!0,type:"string"},{name:"originalSize",description:"Size before re-encoding.",type:"integer"},{name:"encodedSize",description:"Size after re-encoding.",type:"integer"}]}]},{domain:"Browser",description:"The Browser domain defines methods and events for browser managing.",types:[{id:"WindowID",experimental:!0,type:"integer"},{id:"WindowState",description:"The state of the browser window.",experimental:!0,type:"string",enum:["normal","minimized","maximized","fullscreen"]},{id:"Bounds",description:"Browser window bounds information",experimental:!0,type:"object",properties:[{name:"left",description:"The offset from the left edge of the screen to the window in pixels.",optional:!0,type:"integer"},{name:"top",description:"The offset from the top edge of the screen to the window in pixels.",optional:!0,type:"integer"},{name:"width",description:"The window width in pixels.",optional:!0,type:"integer"},{name:"height",description:"The window height in pixels.",optional:!0,type:"integer"},{name:"windowState",description:"The window state. Default to normal.",optional:!0,$ref:"WindowState"}]},{id:"Bucket",description:"Chrome histogram bucket.",experimental:!0,type:"object",properties:[{name:"low",description:"Minimum value (inclusive).",type:"integer"},{name:"high",description:"Maximum value (exclusive).",type:"integer"},{name:"count",description:"Number of samples.",type:"integer"}]},{id:"Histogram",description:"Chrome histogram.",experimental:!0,type:"object",properties:[{name:"name",description:"Name.",type:"string"},{name:"sum",description:"Sum of sample values.",type:"integer"},{name:"count",description:"Total number of samples.",type:"integer"},{name:"buckets",description:"Buckets.",type:"array",items:{$ref:"Bucket"}}]}],commands:[{name:"close",description:"Close browser gracefully."},{name:"getVersion",description:"Returns version information.",returns:[{name:"protocolVersion",description:"Protocol version.",type:"string"},{name:"product",description:"Product name.",type:"string"},{name:"revision",description:"Product revision.",type:"string"},{name:"userAgent",description:"User-Agent.",type:"string"},{name:"jsVersion",description:"V8 version.",type:"string"}]},{name:"getBrowserCommandLine",description:"Returns the command line switches for the browser process if, and only if\n--enable-automation is on the commandline.",experimental:!0,returns:[{name:"arguments",description:"Commandline parameters",type:"array",items:{type:"string"}}]},{name:"getHistograms",description:"Get Chrome histograms.",experimental:!0,parameters:[{name:"query",description:"Requested substring in name. Only histograms which have query as a\nsubstring in their name are extracted. An empty or absent query returns\nall histograms.",optional:!0,type:"string"},{name:"delta",description:"If true, retrieve delta since last call.",optional:!0,type:"boolean"}],returns:[{name:"histograms",description:"Histograms.",type:"array",items:{$ref:"Histogram"}}]},{name:"getHistogram",description:"Get a Chrome histogram by name.",experimental:!0,parameters:[{name:"name",description:"Requested histogram name.",type:"string"},{name:"delta",description:"If true, retrieve delta since last call.",optional:!0,type:"boolean"}],returns:[{name:"histogram",description:"Histogram.",$ref:"Histogram"}]},{name:"getWindowBounds",description:"Get position and size of the browser window.",experimental:!0,parameters:[{name:"windowId",description:"Browser window id.",$ref:"WindowID"}],returns:[{name:"bounds",description:"Bounds information of the window. When window state is 'minimized', the restored window\nposition and size are returned.",$ref:"Bounds"}]},{name:"getWindowForTarget",description:"Get the browser window that contains the devtools target.",experimental:!0,parameters:[{name:"targetId",description:"Devtools agent host id.",$ref:"Target.TargetID"}],returns:[{name:"windowId",description:"Browser window id.",$ref:"WindowID"},{name:"bounds",description:"Bounds information of the window. When window state is 'minimized', the restored window\nposition and size are returned.",$ref:"Bounds"}]},{name:"setWindowBounds",description:"Set position and/or size of the browser window.",experimental:!0,parameters:[{name:"windowId",description:"Browser window id.",$ref:"WindowID"},{name:"bounds",description:"New window bounds. The 'minimized', 'maximized' and 'fullscreen' states cannot be combined\nwith 'left', 'top', 'width' or 'height'. Leaves unspecified fields unchanged.",$ref:"Bounds"}]}]},{domain:"CSS",description:"This domain exposes CSS read/write operations. All CSS objects (stylesheets, rules, and styles)\nhave an associated `id` used in subsequent operations on the related object. Each object type has\na specific `id` structure, and those are not interchangeable between objects of different kinds.\nCSS objects can be loaded using the `get*ForNode()` calls (which accept a DOM node id). A client\ncan also keep track of stylesheets via the `styleSheetAdded`/`styleSheetRemoved` events and\nsubsequently load the required stylesheet contents using the `getStyleSheet[Text]()` methods.",experimental:!0,dependencies:["DOM"],types:[{id:"StyleSheetId",type:"string"},{id:"StyleSheetOrigin",description:'Stylesheet type: "injected" for stylesheets injected via extension, "user-agent" for user-agent\nstylesheets, "inspector" for stylesheets created by the inspector (i.e. those holding the "via\ninspector" rules), "regular" for regular stylesheets.',type:"string",enum:["injected","user-agent","inspector","regular"]},{id:"PseudoElementMatches",description:"CSS rule collection for a single pseudo style.",type:"object",properties:[{name:"pseudoType",description:"Pseudo element type.",$ref:"DOM.PseudoType"},{name:"matches",description:"Matches of CSS rules applicable to the pseudo style.",type:"array",items:{$ref:"RuleMatch"}}]},{id:"InheritedStyleEntry",description:"Inherited CSS rule collection from ancestor node.",type:"object",properties:[{name:"inlineStyle",description:"The ancestor node's inline style, if any, in the style inheritance chain.",optional:!0,$ref:"CSSStyle"},{name:"matchedCSSRules",description:"Matches of CSS rules matching the ancestor node in the style inheritance chain.",type:"array",items:{$ref:"RuleMatch"}}]},{id:"RuleMatch",description:"Match data for a CSS rule.",type:"object",properties:[{name:"rule",description:"CSS rule in the match.",$ref:"CSSRule"},{name:"matchingSelectors",description:"Matching selector indices in the rule's selectorList selectors (0-based).",type:"array",items:{type:"integer"}}]},{id:"Value",description:"Data for a simple selector (these are delimited by commas in a selector list).",type:"object",properties:[{name:"text",description:"Value text.",type:"string"},{name:"range",description:"Value range in the underlying resource (if available).",optional:!0,$ref:"SourceRange"}]},{id:"SelectorList",description:"Selector list data.",type:"object",properties:[{name:"selectors",description:"Selectors in the list.",type:"array",items:{$ref:"Value"}},{name:"text",description:"Rule selector text.",type:"string"}]},{id:"CSSStyleSheetHeader",description:"CSS stylesheet metainformation.",type:"object",properties:[{name:"styleSheetId",description:"The stylesheet identifier.",$ref:"StyleSheetId"},{name:"frameId",description:"Owner frame identifier.",$ref:"Page.FrameId"},{name:"sourceURL",description:"Stylesheet resource URL.",type:"string"},{name:"sourceMapURL",description:"URL of source map associated with the stylesheet (if any).",optional:!0,type:"string"},{name:"origin",description:"Stylesheet origin.",$ref:"StyleSheetOrigin"},{name:"title",description:"Stylesheet title.",type:"string"},{name:"ownerNode",description:"The backend id for the owner node of the stylesheet.",optional:!0,$ref:"DOM.BackendNodeId"},{name:"disabled",description:"Denotes whether the stylesheet is disabled.",type:"boolean"},{name:"hasSourceURL",description:"Whether the sourceURL field value comes from the sourceURL comment.",optional:!0,type:"boolean"},{name:"isInline",description:"Whether this stylesheet is created for STYLE tag by parser. This flag is not set for\ndocument.written STYLE tags.",type:"boolean"},{name:"startLine",description:"Line offset of the stylesheet within the resource (zero based).",type:"number"},{name:"startColumn",description:"Column offset of the stylesheet within the resource (zero based).",type:"number"},{name:"length",description:"Size of the content (in characters).",type:"number"}]},{id:"CSSRule",description:"CSS rule representation.",type:"object",properties:[{name:"styleSheetId",description:"The css style sheet identifier (absent for user agent stylesheet and user-specified\nstylesheet rules) this rule came from.",optional:!0,$ref:"StyleSheetId"},{name:"selectorList",description:"Rule selector data.",$ref:"SelectorList"},{name:"origin",description:"Parent stylesheet's origin.",$ref:"StyleSheetOrigin"},{name:"style",description:"Associated style declaration.",$ref:"CSSStyle"},{name:"media",description:"Media list array (for rules involving media queries). The array enumerates media queries\nstarting with the innermost one, going outwards.",optional:!0,type:"array",items:{$ref:"CSSMedia"}}]},{id:"RuleUsage",description:"CSS coverage information.",type:"object",properties:[{name:"styleSheetId",description:"The css style sheet identifier (absent for user agent stylesheet and user-specified\nstylesheet rules) this rule came from.",$ref:"StyleSheetId"},{name:"startOffset",description:"Offset of the start of the rule (including selector) from the beginning of the stylesheet.",type:"number"},{name:"endOffset",description:"Offset of the end of the rule body from the beginning of the stylesheet.",type:"number"},{name:"used",description:"Indicates whether the rule was actually used by some element in the page.",type:"boolean"}]},{id:"SourceRange",description:"Text range within a resource. All numbers are zero-based.",type:"object",properties:[{name:"startLine",description:"Start line of range.",type:"integer"},{name:"startColumn",description:"Start column of range (inclusive).",type:"integer"},{name:"endLine",description:"End line of range",type:"integer"},{name:"endColumn",description:"End column of range (exclusive).",type:"integer"}]},{id:"ShorthandEntry",type:"object",properties:[{name:"name",description:"Shorthand name.",type:"string"},{name:"value",description:"Shorthand value.",type:"string"},{name:"important",description:'Whether the property has "!important" annotation (implies `false` if absent).',optional:!0,type:"boolean"}]},{id:"CSSComputedStyleProperty",type:"object",properties:[{name:"name",description:"Computed style property name.",type:"string"},{name:"value",description:"Computed style property value.",type:"string"}]},{id:"CSSStyle",description:"CSS style representation.",type:"object",properties:[{name:"styleSheetId",description:"The css style sheet identifier (absent for user agent stylesheet and user-specified\nstylesheet rules) this rule came from.",optional:!0,$ref:"StyleSheetId"},{name:"cssProperties",description:"CSS properties in the style.",type:"array",items:{$ref:"CSSProperty"}},{name:"shorthandEntries",description:"Computed values for all shorthands found in the style.",type:"array",items:{$ref:"ShorthandEntry"}},{name:"cssText",description:"Style declaration text (if available).",optional:!0,type:"string"},{name:"range",description:"Style declaration range in the enclosing stylesheet (if available).",optional:!0,$ref:"SourceRange"}]},{id:"CSSProperty",description:"CSS property declaration data.",type:"object",properties:[{name:"name",description:"The property name.",type:"string"},{name:"value",description:"The property value.",type:"string"},{name:"important",description:'Whether the property has "!important" annotation (implies `false` if absent).',optional:!0,type:"boolean"},{name:"implicit",description:"Whether the property is implicit (implies `false` if absent).",optional:!0,type:"boolean"},{name:"text",description:"The full property text as specified in the style.",optional:!0,type:"string"},{name:"parsedOk",description:"Whether the property is understood by the browser (implies `true` if absent).",optional:!0,type:"boolean"},{name:"disabled",description:"Whether the property is disabled by the user (present for source-based properties only).",optional:!0,type:"boolean"},{name:"range",description:"The entire property range in the enclosing style declaration (if available).",optional:!0,$ref:"SourceRange"}]},{id:"CSSMedia",description:"CSS media rule descriptor.",type:"object",properties:[{name:"text",description:"Media query text.",type:"string"},{name:"source",description:'Source of the media query: "mediaRule" if specified by a @media rule, "importRule" if\nspecified by an @import rule, "linkedSheet" if specified by a "media" attribute in a linked\nstylesheet\'s LINK tag, "inlineSheet" if specified by a "media" attribute in an inline\nstylesheet\'s STYLE tag.',type:"string",enum:["mediaRule","importRule","linkedSheet","inlineSheet"]},{name:"sourceURL",description:"URL of the document containing the media query description.",optional:!0,type:"string"},{name:"range",description:"The associated rule (@media or @import) header range in the enclosing stylesheet (if\navailable).",optional:!0,$ref:"SourceRange"},{name:"styleSheetId",description:"Identifier of the stylesheet containing this object (if exists).",optional:!0,$ref:"StyleSheetId"},{name:"mediaList",description:"Array of media queries.",optional:!0,type:"array",items:{$ref:"MediaQuery"}}]},{id:"MediaQuery",description:"Media query descriptor.",type:"object",properties:[{name:"expressions",description:"Array of media query expressions.",type:"array",items:{$ref:"MediaQueryExpression"}},{name:"active",description:"Whether the media query condition is satisfied.",type:"boolean"}]},{id:"MediaQueryExpression",description:"Media query expression descriptor.",type:"object",properties:[{name:"value",description:"Media query expression value.",type:"number"},{name:"unit",description:"Media query expression units.",type:"string"},{name:"feature",description:"Media query expression feature.",type:"string"},{name:"valueRange",description:"The associated range of the value text in the enclosing stylesheet (if available).",optional:!0,$ref:"SourceRange"},{name:"computedLength",description:"Computed length of media query expression (if applicable).",optional:!0,type:"number"}]},{id:"PlatformFontUsage",description:"Information about amount of glyphs that were rendered with given font.",type:"object",properties:[{name:"familyName",description:"Font's family name reported by platform.",type:"string"},{name:"isCustomFont",description:"Indicates if the font was downloaded or resolved locally.",type:"boolean"},{name:"glyphCount",description:"Amount of glyphs that were rendered with this font.",type:"number"}]},{id:"FontFace",description:"Properties of a web font: https://www.w3.org/TR/2008/REC-CSS2-20080411/fonts.html#font-descriptions",type:"object",properties:[{name:"fontFamily",description:"The font-family.",type:"string"},{name:"fontStyle",description:"The font-style.",type:"string"},{name:"fontVariant",description:"The font-variant.",type:"string"},{name:"fontWeight",description:"The font-weight.",type:"string"},{name:"fontStretch",description:"The font-stretch.",type:"string"},{name:"unicodeRange",description:"The unicode-range.",type:"string"},{name:"src",description:"The src.",type:"string"},{name:"platformFontFamily",description:"The resolved platform font family",type:"string"}]},{id:"CSSKeyframesRule",description:"CSS keyframes rule representation.",type:"object",properties:[{name:"animationName",description:"Animation name.",$ref:"Value"},{name:"keyframes",description:"List of keyframes.",type:"array",items:{$ref:"CSSKeyframeRule"}}]},{id:"CSSKeyframeRule",description:"CSS keyframe rule representation.",type:"object",properties:[{name:"styleSheetId",description:"The css style sheet identifier (absent for user agent stylesheet and user-specified\nstylesheet rules) this rule came from.",optional:!0,$ref:"StyleSheetId"},{name:"origin",description:"Parent stylesheet's origin.",$ref:"StyleSheetOrigin"},{name:"keyText",description:"Associated key text.",$ref:"Value"},{name:"style",description:"Associated style declaration.",$ref:"CSSStyle"}]},{id:"StyleDeclarationEdit",description:"A descriptor of operation to mutate style declaration text.",type:"object",properties:[{name:"styleSheetId",description:"The css style sheet identifier.",$ref:"StyleSheetId"},{name:"range",description:"The range of the style text in the enclosing stylesheet.",$ref:"SourceRange"},{name:"text",description:"New style text.",type:"string"}]}],commands:[{name:"addRule",description:"Inserts a new rule with the given `ruleText` in a stylesheet with given `styleSheetId`, at the\nposition specified by `location`.",parameters:[{name:"styleSheetId",description:"The css style sheet identifier where a new rule should be inserted.",$ref:"StyleSheetId"},{name:"ruleText",description:"The text of a new rule.",type:"string"},{name:"location",description:"Text position of a new rule in the target style sheet.",$ref:"SourceRange"}],returns:[{name:"rule",description:"The newly created rule.",$ref:"CSSRule"}]},{name:"collectClassNames",description:"Returns all class names from specified stylesheet.",parameters:[{name:"styleSheetId",$ref:"StyleSheetId"}],returns:[{name:"classNames",description:"Class name list.",type:"array",items:{type:"string"}}]},{name:"createStyleSheet",description:'Creates a new special "via-inspector" stylesheet in the frame with given `frameId`.',parameters:[{name:"frameId",description:'Identifier of the frame where "via-inspector" stylesheet should be created.',$ref:"Page.FrameId"}],returns:[{name:"styleSheetId",description:'Identifier of the created "via-inspector" stylesheet.',$ref:"StyleSheetId"}]},{name:"disable",description:"Disables the CSS agent for the given page."},{name:"enable",description:"Enables the CSS agent for the given page. Clients should not assume that the CSS agent has been\nenabled until the result of this command is received."},{name:"forcePseudoState",description:"Ensures that the given node will have specified pseudo-classes whenever its style is computed by\nthe browser.",parameters:[{name:"nodeId",description:"The element id for which to force the pseudo state.",$ref:"DOM.NodeId"},{name:"forcedPseudoClasses",description:"Element pseudo classes to force when computing the element's style.",type:"array",items:{type:"string"}}]},{name:"getBackgroundColors",parameters:[{name:"nodeId",description:"Id of the node to get background colors for.",$ref:"DOM.NodeId"}],returns:[{name:"backgroundColors",description:"The range of background colors behind this element, if it contains any visible text. If no\nvisible text is present, this will be undefined. In the case of a flat background color,\nthis will consist of simply that color. In the case of a gradient, this will consist of each\nof the color stops. For anything more complicated, this will be an empty array. Images will\nbe ignored (as if the image had failed to load).",optional:!0,type:"array",items:{type:"string"}},{name:"computedFontSize",description:"The computed font size for this node, as a CSS computed value string (e.g. '12px').",optional:!0,type:"string"},{name:"computedFontWeight",description:"The computed font weight for this node, as a CSS computed value string (e.g. 'normal' or\n'100').",optional:!0,type:"string"},{name:"computedBodyFontSize",description:"The computed font size for the document body, as a computed CSS value string (e.g. '16px').",optional:!0,type:"string"}]},{name:"getComputedStyleForNode",description:"Returns the computed style for a DOM node identified by `nodeId`.",parameters:[{name:"nodeId",$ref:"DOM.NodeId"}],returns:[{name:"computedStyle",description:"Computed style for the specified DOM node.",type:"array",items:{$ref:"CSSComputedStyleProperty"}}]},{name:"getInlineStylesForNode",description:'Returns the styles defined inline (explicitly in the "style" attribute and implicitly, using DOM\nattributes) for a DOM node identified by `nodeId`.',parameters:[{name:"nodeId",$ref:"DOM.NodeId"}],returns:[{name:"inlineStyle",description:"Inline style for the specified DOM node.",optional:!0,$ref:"CSSStyle"},{name:"attributesStyle",description:'Attribute-defined element style (e.g. resulting from "width=20 height=100%").',optional:!0,$ref:"CSSStyle"}]},{name:"getMatchedStylesForNode",description:"Returns requested styles for a DOM node identified by `nodeId`.",parameters:[{name:"nodeId",$ref:"DOM.NodeId"}],returns:[{name:"inlineStyle",description:"Inline style for the specified DOM node.",optional:!0,$ref:"CSSStyle"},{name:"attributesStyle",description:'Attribute-defined element style (e.g. resulting from "width=20 height=100%").',optional:!0,$ref:"CSSStyle"},{name:"matchedCSSRules",description:"CSS rules matching this node, from all applicable stylesheets.",optional:!0,type:"array",items:{$ref:"RuleMatch"}},{name:"pseudoElements",description:"Pseudo style matches for this node.",optional:!0,type:"array",items:{$ref:"PseudoElementMatches"}},{name:"inherited",description:"A chain of inherited styles (from the immediate node parent up to the DOM tree root).",optional:!0,type:"array",items:{$ref:"InheritedStyleEntry"}},{name:"cssKeyframesRules",description:"A list of CSS keyframed animations matching this node.",optional:!0,type:"array",items:{$ref:"CSSKeyframesRule"}}]},{name:"getMediaQueries",description:"Returns all media queries parsed by the rendering engine.",returns:[{name:"medias",type:"array",items:{$ref:"CSSMedia"}}]},{name:"getPlatformFontsForNode",description:"Requests information about platform fonts which we used to render child TextNodes in the given\nnode.",parameters:[{name:"nodeId",$ref:"DOM.NodeId"}],returns:[{name:"fonts",description:"Usage statistics for every employed platform font.",type:"array",items:{$ref:"PlatformFontUsage"}}]},{name:"getStyleSheetText",description:"Returns the current textual content for a stylesheet.",parameters:[{name:"styleSheetId",$ref:"StyleSheetId"}],returns:[{name:"text",description:"The stylesheet text.",type:"string"}]},{name:"setEffectivePropertyValueForNode",description:"Find a rule with the given active property for the given node and set the new value for this\nproperty",parameters:[{name:"nodeId",description:"The element id for which to set property.",$ref:"DOM.NodeId"},{name:"propertyName",type:"string"},{name:"value",type:"string"}]},{name:"setKeyframeKey",description:"Modifies the keyframe rule key text.",parameters:[{name:"styleSheetId",$ref:"StyleSheetId"},{name:"range",$ref:"SourceRange"},{name:"keyText",type:"string"}],returns:[{name:"keyText",description:"The resulting key text after modification.",$ref:"Value"}]},{name:"setMediaText",description:"Modifies the rule selector.",parameters:[{name:"styleSheetId",$ref:"StyleSheetId"},{name:"range",$ref:"SourceRange"},{name:"text",type:"string"}],returns:[{name:"media",description:"The resulting CSS media rule after modification.",$ref:"CSSMedia"}]},{name:"setRuleSelector",description:"Modifies the rule selector.",parameters:[{name:"styleSheetId",$ref:"StyleSheetId"},{name:"range",$ref:"SourceRange"},{name:"selector",type:"string"}],returns:[{name:"selectorList",description:"The resulting selector list after modification.",$ref:"SelectorList"}]},{name:"setStyleSheetText",description:"Sets the new stylesheet text.",parameters:[{name:"styleSheetId",$ref:"StyleSheetId"},{name:"text",type:"string"}],returns:[{name:"sourceMapURL",description:"URL of source map associated with script (if any).",optional:!0,type:"string"}]},{name:"setStyleTexts",description:"Applies specified style edits one after another in the given order.",parameters:[{name:"edits",type:"array",items:{$ref:"StyleDeclarationEdit"}}],returns:[{name:"styles",description:"The resulting styles after modification.",type:"array",items:{$ref:"CSSStyle"}}]},{name:"startRuleUsageTracking",description:"Enables the selector recording."},{name:"stopRuleUsageTracking",description:"Stop tracking rule usage and return the list of rules that were used since last call to\n`takeCoverageDelta` (or since start of coverage instrumentation)",returns:[{name:"ruleUsage",type:"array",items:{$ref:"RuleUsage"}}]},{name:"takeCoverageDelta",description:"Obtain list of rules that became used since last call to this method (or since start of coverage\ninstrumentation)",returns:[{name:"coverage",type:"array",items:{$ref:"RuleUsage"}}]}],events:[{name:"fontsUpdated",description:"Fires whenever a web font is updated.  A non-empty font parameter indicates a successfully loaded\nweb font",parameters:[{name:"font",description:"The web font that has loaded.",optional:!0,$ref:"FontFace"}]},{name:"mediaQueryResultChanged",description:"Fires whenever a MediaQuery result changes (for example, after a browser window has been\nresized.) The current implementation considers only viewport-dependent media features."},{name:"styleSheetAdded",description:"Fired whenever an active document stylesheet is added.",parameters:[{name:"header",description:"Added stylesheet metainfo.",$ref:"CSSStyleSheetHeader"}]},{name:"styleSheetChanged",description:"Fired whenever a stylesheet is changed as a result of the client operation.",parameters:[{name:"styleSheetId",$ref:"StyleSheetId"}]},{name:"styleSheetRemoved",description:"Fired whenever an active document stylesheet is removed.",parameters:[{name:"styleSheetId",description:"Identifier of the removed stylesheet.",$ref:"StyleSheetId"}]}]},{domain:"CacheStorage",experimental:!0,types:[{id:"CacheId",description:"Unique identifier of the Cache object.",type:"string"},{id:"DataEntry",description:"Data entry.",type:"object",properties:[{name:"requestURL",description:"Request URL.",type:"string"},{name:"requestMethod",description:"Request method.",type:"string"},{name:"requestHeaders",description:"Request headers",type:"array",items:{$ref:"Header"}},{name:"responseTime",description:"Number of seconds since epoch.",type:"number"},{name:"responseStatus",description:"HTTP response status code.",type:"integer"},{name:"responseStatusText",description:"HTTP response status text.",type:"string"},{name:"responseHeaders",description:"Response headers",type:"array",items:{$ref:"Header"}}]},{id:"Cache",description:"Cache identifier.",type:"object",properties:[{name:"cacheId",description:"An opaque unique id of the cache.",$ref:"CacheId"},{name:"securityOrigin",description:"Security origin of the cache.",type:"string"},{name:"cacheName",description:"The name of the cache.",type:"string"}]},{id:"Header",type:"object",properties:[{name:"name",type:"string"},{name:"value",type:"string"}]},{id:"CachedResponse",description:"Cached response",type:"object",properties:[{name:"body",description:"Entry content, base64-encoded.",type:"string"}]}],commands:[{name:"deleteCache",description:"Deletes a cache.",parameters:[{name:"cacheId",description:"Id of cache for deletion.",$ref:"CacheId"}]},{name:"deleteEntry",description:"Deletes a cache entry.",parameters:[{name:"cacheId",description:"Id of cache where the entry will be deleted.",$ref:"CacheId"},{name:"request",description:"URL spec of the request.",type:"string"}]},{name:"requestCacheNames",description:"Requests cache names.",parameters:[{name:"securityOrigin",description:"Security origin.",type:"string"}],returns:[{name:"caches",description:"Caches for the security origin.",type:"array",items:{$ref:"Cache"}}]},{name:"requestCachedResponse",description:"Fetches cache entry.",parameters:[{name:"cacheId",description:"Id of cache that contains the enty.",$ref:"CacheId"},{name:"requestURL",description:"URL spec of the request.",type:"string"}],returns:[{name:"response",description:"Response read from the cache.",$ref:"CachedResponse"}]},{name:"requestEntries",description:"Requests data from cache.",parameters:[{name:"cacheId",description:"ID of cache to get entries from.",$ref:"CacheId"},{name:"skipCount",description:"Number of records to skip.",type:"integer"},{name:"pageSize",description:"Number of records to fetch.",type:"integer"}],returns:[{name:"cacheDataEntries",description:"Array of object store data entries.",type:"array",items:{$ref:"DataEntry"}},{name:"hasMore",description:"If true, there are more entries to fetch in the given range.",type:"boolean"}]}]},{domain:"DOM",description:"This domain exposes DOM read/write operations. Each DOM Node is represented with its mirror object\nthat has an `id`. This `id` can be used to get additional information on the Node, resolve it into\nthe JavaScript object wrapper, etc. It is important that client receives DOM events only for the\nnodes that are known to the client. Backend keeps track of the nodes that were sent to the client\nand never sends the same node twice. It is client's responsibility to collect information about\nthe nodes that were sent to the client.<p>Note that `iframe` owner elements will return\ncorresponding document elements as their child nodes.</p>",dependencies:["Runtime"],types:[{id:"NodeId",description:"Unique DOM node identifier.",type:"integer"},{id:"BackendNodeId",description:"Unique DOM node identifier used to reference a node that may not have been pushed to the\nfront-end.",type:"integer"},{id:"BackendNode",description:"Backend node with a friendly name.",type:"object",properties:[{name:"nodeType",description:"`Node`'s nodeType.",type:"integer"},{name:"nodeName",description:"`Node`'s nodeName.",type:"string"},{name:"backendNodeId",$ref:"BackendNodeId"
}]},{id:"PseudoType",description:"Pseudo element type.",type:"string",enum:["first-line","first-letter","before","after","backdrop","selection","first-line-inherited","scrollbar","scrollbar-thumb","scrollbar-button","scrollbar-track","scrollbar-track-piece","scrollbar-corner","resizer","input-list-button"]},{id:"ShadowRootType",description:"Shadow root type.",type:"string",enum:["user-agent","open","closed"]},{id:"Node",description:"DOM interaction is implemented in terms of mirror objects that represent the actual DOM nodes.\nDOMNode is a base node mirror type.",type:"object",properties:[{name:"nodeId",description:"Node identifier that is passed into the rest of the DOM messages as the `nodeId`. Backend\nwill only push node with given `id` once. It is aware of all requested nodes and will only\nfire DOM events for nodes known to the client.",$ref:"NodeId"},{name:"parentId",description:"The id of the parent node if any.",optional:!0,$ref:"NodeId"},{name:"backendNodeId",description:"The BackendNodeId for this node.",$ref:"BackendNodeId"},{name:"nodeType",description:"`Node`'s nodeType.",type:"integer"},{name:"nodeName",description:"`Node`'s nodeName.",type:"string"},{name:"localName",description:"`Node`'s localName.",type:"string"},{name:"nodeValue",description:"`Node`'s nodeValue.",type:"string"},{name:"childNodeCount",description:"Child count for `Container` nodes.",optional:!0,type:"integer"},{name:"children",description:"Child nodes of this node when requested with children.",optional:!0,type:"array",items:{$ref:"Node"}},{name:"attributes",description:"Attributes of the `Element` node in the form of flat array `[name1, value1, name2, value2]`.",optional:!0,type:"array",items:{type:"string"}},{name:"documentURL",description:"Document URL that `Document` or `FrameOwner` node points to.",optional:!0,type:"string"},{name:"baseURL",description:"Base URL that `Document` or `FrameOwner` node uses for URL completion.",optional:!0,type:"string"},{name:"publicId",description:"`DocumentType`'s publicId.",optional:!0,type:"string"},{name:"systemId",description:"`DocumentType`'s systemId.",optional:!0,type:"string"},{name:"internalSubset",description:"`DocumentType`'s internalSubset.",optional:!0,type:"string"},{name:"xmlVersion",description:"`Document`'s XML version in case of XML documents.",optional:!0,type:"string"},{name:"name",description:"`Attr`'s name.",optional:!0,type:"string"},{name:"value",description:"`Attr`'s value.",optional:!0,type:"string"},{name:"pseudoType",description:"Pseudo element type for this node.",optional:!0,$ref:"PseudoType"},{name:"shadowRootType",description:"Shadow root type.",optional:!0,$ref:"ShadowRootType"},{name:"frameId",description:"Frame ID for frame owner elements.",optional:!0,$ref:"Page.FrameId"},{name:"contentDocument",description:"Content document for frame owner elements.",optional:!0,$ref:"Node"},{name:"shadowRoots",description:"Shadow root list for given element host.",optional:!0,type:"array",items:{$ref:"Node"}},{name:"templateContent",description:"Content document fragment for template elements.",optional:!0,$ref:"Node"},{name:"pseudoElements",description:"Pseudo elements associated with this node.",optional:!0,type:"array",items:{$ref:"Node"}},{name:"importedDocument",description:"Import document for the HTMLImport links.",optional:!0,$ref:"Node"},{name:"distributedNodes",description:"Distributed nodes for given insertion point.",optional:!0,type:"array",items:{$ref:"BackendNode"}},{name:"isSVG",description:"Whether the node is SVG.",optional:!0,type:"boolean"}]},{id:"RGBA",description:"A structure holding an RGBA color.",type:"object",properties:[{name:"r",description:"The red component, in the [0-255] range.",type:"integer"},{name:"g",description:"The green component, in the [0-255] range.",type:"integer"},{name:"b",description:"The blue component, in the [0-255] range.",type:"integer"},{name:"a",description:"The alpha component, in the [0-1] range (default: 1).",optional:!0,type:"number"}]},{id:"Quad",description:"An array of quad vertices, x immediately followed by y for each point, points clock-wise.",type:"array",items:{type:"number"}},{id:"BoxModel",description:"Box model.",type:"object",properties:[{name:"content",description:"Content box",$ref:"Quad"},{name:"padding",description:"Padding box",$ref:"Quad"},{name:"border",description:"Border box",$ref:"Quad"},{name:"margin",description:"Margin box",$ref:"Quad"},{name:"width",description:"Node width",type:"integer"},{name:"height",description:"Node height",type:"integer"},{name:"shapeOutside",description:"Shape outside coordinates",optional:!0,$ref:"ShapeOutsideInfo"}]},{id:"ShapeOutsideInfo",description:"CSS Shape Outside details.",type:"object",properties:[{name:"bounds",description:"Shape bounds",$ref:"Quad"},{name:"shape",description:"Shape coordinate details",type:"array",items:{type:"any"}},{name:"marginShape",description:"Margin shape bounds",type:"array",items:{type:"any"}}]},{id:"Rect",description:"Rectangle.",type:"object",properties:[{name:"x",description:"X coordinate",type:"number"},{name:"y",description:"Y coordinate",type:"number"},{name:"width",description:"Rectangle width",type:"number"},{name:"height",description:"Rectangle height",type:"number"}]}],commands:[{name:"collectClassNamesFromSubtree",description:"Collects class names for the node with given id and all of it's child nodes.",experimental:!0,parameters:[{name:"nodeId",description:"Id of the node to collect class names.",$ref:"NodeId"}],returns:[{name:"classNames",description:"Class name list.",type:"array",items:{type:"string"}}]},{name:"copyTo",description:"Creates a deep copy of the specified node and places it into the target container before the\ngiven anchor.",experimental:!0,parameters:[{name:"nodeId",description:"Id of the node to copy.",$ref:"NodeId"},{name:"targetNodeId",description:"Id of the element to drop the copy into.",$ref:"NodeId"},{name:"insertBeforeNodeId",description:"Drop the copy before this node (if absent, the copy becomes the last child of\n`targetNodeId`).",optional:!0,$ref:"NodeId"}],returns:[{name:"nodeId",description:"Id of the node clone.",$ref:"NodeId"}]},{name:"describeNode",description:"Describes node given its id, does not require domain to be enabled. Does not start tracking any\nobjects, can be used for automation.",parameters:[{name:"nodeId",description:"Identifier of the node.",optional:!0,$ref:"NodeId"},{name:"backendNodeId",description:"Identifier of the backend node.",optional:!0,$ref:"BackendNodeId"},{name:"objectId",description:"JavaScript object id of the node wrapper.",optional:!0,$ref:"Runtime.RemoteObjectId"},{name:"depth",description:"The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the\nentire subtree or provide an integer larger than 0.",optional:!0,type:"integer"},{name:"pierce",description:"Whether or not iframes and shadow roots should be traversed when returning the subtree\n(default is false).",optional:!0,type:"boolean"}],returns:[{name:"node",description:"Node description.",$ref:"Node"}]},{name:"disable",description:"Disables DOM agent for the given page."},{name:"discardSearchResults",description:"Discards search results from the session with the given id. `getSearchResults` should no longer\nbe called for that search.",experimental:!0,parameters:[{name:"searchId",description:"Unique search session identifier.",type:"string"}]},{name:"enable",description:"Enables DOM agent for the given page."},{name:"focus",description:"Focuses the given element.",parameters:[{name:"nodeId",description:"Identifier of the node.",optional:!0,$ref:"NodeId"},{name:"backendNodeId",description:"Identifier of the backend node.",optional:!0,$ref:"BackendNodeId"},{name:"objectId",description:"JavaScript object id of the node wrapper.",optional:!0,$ref:"Runtime.RemoteObjectId"}]},{name:"getAttributes",description:"Returns attributes for the specified node.",parameters:[{name:"nodeId",description:"Id of the node to retrieve attibutes for.",$ref:"NodeId"}],returns:[{name:"attributes",description:"An interleaved array of node attribute names and values.",type:"array",items:{type:"string"}}]},{name:"getBoxModel",description:"Returns boxes for the given node.",parameters:[{name:"nodeId",description:"Identifier of the node.",optional:!0,$ref:"NodeId"},{name:"backendNodeId",description:"Identifier of the backend node.",optional:!0,$ref:"BackendNodeId"},{name:"objectId",description:"JavaScript object id of the node wrapper.",optional:!0,$ref:"Runtime.RemoteObjectId"}],returns:[{name:"model",description:"Box model for the node.",$ref:"BoxModel"}]},{name:"getContentQuads",description:"Returns quads that describe node position on the page. This method\nmight return multiple quads for inline nodes.",experimental:!0,parameters:[{name:"nodeId",description:"Identifier of the node.",optional:!0,$ref:"NodeId"},{name:"backendNodeId",description:"Identifier of the backend node.",optional:!0,$ref:"BackendNodeId"},{name:"objectId",description:"JavaScript object id of the node wrapper.",optional:!0,$ref:"Runtime.RemoteObjectId"}],returns:[{name:"quads",description:"Quads that describe node layout relative to viewport.",type:"array",items:{$ref:"Quad"}}]},{name:"getDocument",description:"Returns the root DOM node (and optionally the subtree) to the caller.",parameters:[{name:"depth",description:"The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the\nentire subtree or provide an integer larger than 0.",optional:!0,type:"integer"},{name:"pierce",description:"Whether or not iframes and shadow roots should be traversed when returning the subtree\n(default is false).",optional:!0,type:"boolean"}],returns:[{name:"root",description:"Resulting node.",$ref:"Node"}]},{name:"getFlattenedDocument",description:"Returns the root DOM node (and optionally the subtree) to the caller.",parameters:[{name:"depth",description:"The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the\nentire subtree or provide an integer larger than 0.",optional:!0,type:"integer"},{name:"pierce",description:"Whether or not iframes and shadow roots should be traversed when returning the subtree\n(default is false).",optional:!0,type:"boolean"}],returns:[{name:"nodes",description:"Resulting node.",type:"array",items:{$ref:"Node"}}]},{name:"getNodeForLocation",description:"Returns node id at given location.",experimental:!0,parameters:[{name:"x",description:"X coordinate.",type:"integer"},{name:"y",description:"Y coordinate.",type:"integer"},{name:"includeUserAgentShadowDOM",description:"False to skip to the nearest non-UA shadow root ancestor (default: false).",optional:!0,type:"boolean"}],returns:[{name:"nodeId",description:"Id of the node at given coordinates.",$ref:"NodeId"}]},{name:"getOuterHTML",description:"Returns node's HTML markup.",parameters:[{name:"nodeId",description:"Identifier of the node.",optional:!0,$ref:"NodeId"},{name:"backendNodeId",description:"Identifier of the backend node.",optional:!0,$ref:"BackendNodeId"},{name:"objectId",description:"JavaScript object id of the node wrapper.",optional:!0,$ref:"Runtime.RemoteObjectId"}],returns:[{name:"outerHTML",description:"Outer HTML markup.",type:"string"}]},{name:"getRelayoutBoundary",description:"Returns the id of the nearest ancestor that is a relayout boundary.",experimental:!0,parameters:[{name:"nodeId",description:"Id of the node.",$ref:"NodeId"}],returns:[{name:"nodeId",description:"Relayout boundary node id for the given node.",$ref:"NodeId"}]},{name:"getSearchResults",description:"Returns search results from given `fromIndex` to given `toIndex` from the search with the given\nidentifier.",experimental:!0,parameters:[{name:"searchId",description:"Unique search session identifier.",type:"string"},{name:"fromIndex",description:"Start index of the search result to be returned.",type:"integer"},{name:"toIndex",description:"End index of the search result to be returned.",type:"integer"}],returns:[{name:"nodeIds",description:"Ids of the search result nodes.",type:"array",items:{$ref:"NodeId"}}]},{name:"hideHighlight",description:"Hides any highlight.",redirect:"Overlay"},{name:"highlightNode",description:"Highlights DOM node.",redirect:"Overlay"},{name:"highlightRect",description:"Highlights given rectangle.",redirect:"Overlay"},{name:"markUndoableState",description:"Marks last undoable state.",experimental:!0},{name:"moveTo",description:"Moves node into the new container, places it before the given anchor.",parameters:[{name:"nodeId",description:"Id of the node to move.",$ref:"NodeId"},{name:"targetNodeId",description:"Id of the element to drop the moved node into.",$ref:"NodeId"},{name:"insertBeforeNodeId",description:"Drop node before this one (if absent, the moved node becomes the last child of\n`targetNodeId`).",optional:!0,$ref:"NodeId"}],returns:[{name:"nodeId",description:"New id of the moved node.",$ref:"NodeId"}]},{name:"performSearch",description:"Searches for a given string in the DOM tree. Use `getSearchResults` to access search results or\n`cancelSearch` to end this search session.",experimental:!0,parameters:[{name:"query",description:"Plain text or query selector or XPath search query.",type:"string"},{name:"includeUserAgentShadowDOM",description:"True to search in user agent shadow DOM.",optional:!0,type:"boolean"}],returns:[{name:"searchId",description:"Unique search session identifier.",type:"string"},{name:"resultCount",description:"Number of search results.",type:"integer"}]},{name:"pushNodeByPathToFrontend",description:"Requests that the node is sent to the caller given its path. // FIXME, use XPath",experimental:!0,parameters:[{name:"path",description:"Path to node in the proprietary format.",type:"string"}],returns:[{name:"nodeId",description:"Id of the node for given path.",$ref:"NodeId"}]},{name:"pushNodesByBackendIdsToFrontend",description:"Requests that a batch of nodes is sent to the caller given their backend node ids.",experimental:!0,parameters:[{name:"backendNodeIds",description:"The array of backend node ids.",type:"array",items:{$ref:"BackendNodeId"}}],returns:[{name:"nodeIds",description:"The array of ids of pushed nodes that correspond to the backend ids specified in\nbackendNodeIds.",type:"array",items:{$ref:"NodeId"}}]},{name:"querySelector",description:"Executes `querySelector` on a given node.",parameters:[{name:"nodeId",description:"Id of the node to query upon.",$ref:"NodeId"},{name:"selector",description:"Selector string.",type:"string"}],returns:[{name:"nodeId",description:"Query selector result.",$ref:"NodeId"}]},{name:"querySelectorAll",description:"Executes `querySelectorAll` on a given node.",parameters:[{name:"nodeId",description:"Id of the node to query upon.",$ref:"NodeId"},{name:"selector",description:"Selector string.",type:"string"}],returns:[{name:"nodeIds",description:"Query selector result.",type:"array",items:{$ref:"NodeId"}}]},{name:"redo",description:"Re-does the last undone action.",experimental:!0},{name:"removeAttribute",description:"Removes attribute with given name from an element with given id.",parameters:[{name:"nodeId",description:"Id of the element to remove attribute from.",$ref:"NodeId"},{name:"name",description:"Name of the attribute to remove.",type:"string"}]},{name:"removeNode",description:"Removes node with given id.",parameters:[{name:"nodeId",description:"Id of the node to remove.",$ref:"NodeId"}]},{name:"requestChildNodes",description:"Requests that children of the node with given id are returned to the caller in form of\n`setChildNodes` events where not only immediate children are retrieved, but all children down to\nthe specified depth.",parameters:[{name:"nodeId",description:"Id of the node to get children for.",$ref:"NodeId"},{name:"depth",description:"The maximum depth at which children should be retrieved, defaults to 1. Use -1 for the\nentire subtree or provide an integer larger than 0.",optional:!0,type:"integer"},{name:"pierce",description:"Whether or not iframes and shadow roots should be traversed when returning the sub-tree\n(default is false).",optional:!0,type:"boolean"}]},{name:"requestNode",description:"Requests that the node is sent to the caller given the JavaScript node object reference. All\nnodes that form the path from the node to the root are also sent to the client as a series of\n`setChildNodes` notifications.",parameters:[{name:"objectId",description:"JavaScript object id to convert into node.",$ref:"Runtime.RemoteObjectId"}],returns:[{name:"nodeId",description:"Node id for given object.",$ref:"NodeId"}]},{name:"resolveNode",description:"Resolves the JavaScript node object for a given NodeId or BackendNodeId.",parameters:[{name:"nodeId",description:"Id of the node to resolve.",optional:!0,$ref:"NodeId"},{name:"backendNodeId",description:"Backend identifier of the node to resolve.",optional:!0,$ref:"DOM.BackendNodeId"},{name:"objectGroup",description:"Symbolic group name that can be used to release multiple objects.",optional:!0,type:"string"}],returns:[{name:"object",description:"JavaScript object wrapper for given node.",$ref:"Runtime.RemoteObject"}]},{name:"setAttributeValue",description:"Sets attribute for an element with given id.",parameters:[{name:"nodeId",description:"Id of the element to set attribute for.",$ref:"NodeId"},{name:"name",description:"Attribute name.",type:"string"},{name:"value",description:"Attribute value.",type:"string"}]},{name:"setAttributesAsText",description:"Sets attributes on element with given id. This method is useful when user edits some existing\nattribute value and types in several attribute name/value pairs.",parameters:[{name:"nodeId",description:"Id of the element to set attributes for.",$ref:"NodeId"},{name:"text",description:"Text with a number of attributes. Will parse this text using HTML parser.",type:"string"},{name:"name",description:"Attribute name to replace with new attributes derived from text in case text parsed\nsuccessfully.",optional:!0,type:"string"}]},{name:"setFileInputFiles",description:"Sets files for the given file input element.",parameters:[{name:"files",description:"Array of file paths to set.",type:"array",items:{type:"string"}},{name:"nodeId",description:"Identifier of the node.",optional:!0,$ref:"NodeId"},{name:"backendNodeId",description:"Identifier of the backend node.",optional:!0,$ref:"BackendNodeId"},{name:"objectId",description:"JavaScript object id of the node wrapper.",optional:!0,$ref:"Runtime.RemoteObjectId"}]},{name:"setInspectedNode",description:"Enables console to refer to the node with given id via $x (see Command Line API for more details\n$x functions).",experimental:!0,parameters:[{name:"nodeId",description:"DOM node id to be accessible by means of $x command line API.",$ref:"NodeId"}]},{name:"setNodeName",description:"Sets node name for a node with given id.",parameters:[{name:"nodeId",description:"Id of the node to set name for.",$ref:"NodeId"},{name:"name",description:"New node's name.",type:"string"}],returns:[{name:"nodeId",description:"New node's id.",$ref:"NodeId"}]},{name:"setNodeValue",description:"Sets node value for a node with given id.",parameters:[{name:"nodeId",description:"Id of the node to set value for.",$ref:"NodeId"},{name:"value",description:"New node's value.",type:"string"}]},{name:"setOuterHTML",description:"Sets node HTML markup, returns new node id.",parameters:[{name:"nodeId",description:"Id of the node to set markup for.",$ref:"NodeId"},{name:"outerHTML",description:"Outer HTML markup to set.",type:"string"}]},{name:"undo",description:"Undoes the last performed action.",experimental:!0},{name:"getFrameOwner",description:"Returns iframe node that owns iframe with the given domain.",experimental:!0,parameters:[{name:"frameId",$ref:"Page.FrameId"}],returns:[{name:"nodeId",$ref:"NodeId"}]}],events:[{name:"attributeModified",description:"Fired when `Element`'s attribute is modified.",parameters:[{name:"nodeId",description:"Id of the node that has changed.",$ref:"NodeId"},{name:"name",description:"Attribute name.",type:"string"},{name:"value",description:"Attribute value.",type:"string"}]},{name:"attributeRemoved",description:"Fired when `Element`'s attribute is removed.",parameters:[{name:"nodeId",description:"Id of the node that has changed.",$ref:"NodeId"},{name:"name",description:"A ttribute name.",type:"string"}]},{name:"characterDataModified",description:"Mirrors `DOMCharacterDataModified` event.",parameters:[{name:"nodeId",description:"Id of the node that has changed.",$ref:"NodeId"},{name:"characterData",description:"New text value.",type:"string"}]},{name:"childNodeCountUpdated",description:"Fired when `Container`'s child node count has changed.",parameters:[{name:"nodeId",description:"Id of the node that has changed.",$ref:"NodeId"},{name:"childNodeCount",description:"New node count.",type:"integer"}]},{name:"childNodeInserted",description:"Mirrors `DOMNodeInserted` event.",parameters:[{name:"parentNodeId",description:"Id of the node that has changed.",$ref:"NodeId"},{name:"previousNodeId",description:"If of the previous siblint.",$ref:"NodeId"},{name:"node",description:"Inserted node data.",$ref:"Node"}]},{name:"childNodeRemoved",description:"Mirrors `DOMNodeRemoved` event.",parameters:[{name:"parentNodeId",description:"Parent id.",$ref:"NodeId"},{name:"nodeId",description:"Id of the node that has been removed.",$ref:"NodeId"}]},{name:"distributedNodesUpdated",description:"Called when distrubution is changed.",experimental:!0,parameters:[{name:"insertionPointId",description:"Insertion point where distrubuted nodes were updated.",$ref:"NodeId"},{name:"distributedNodes",description:"Distributed nodes for given insertion point.",type:"array",items:{$ref:"BackendNode"}}]},{name:"documentUpdated",description:"Fired when `Document` has been totally updated. Node ids are no longer valid."},{name:"inlineStyleInvalidated",description:"Fired when `Element`'s inline style is modified via a CSS property modification.",experimental:!0,parameters:[{name:"nodeIds",description:"Ids of the nodes for which the inline styles have been invalidated.",type:"array",items:{$ref:"NodeId"}}]},{name:"pseudoElementAdded",description:"Called when a pseudo element is added to an element.",experimental:!0,parameters:[{name:"parentId",description:"Pseudo element's parent element id.",$ref:"NodeId"},{name:"pseudoElement",description:"The added pseudo element.",$ref:"Node"}]},{name:"pseudoElementRemoved",description:"Called when a pseudo element is removed from an element.",experimental:!0,parameters:[{name:"parentId",description:"Pseudo element's parent element id.",$ref:"NodeId"},{name:"pseudoElementId",description:"The removed pseudo element id.",$ref:"NodeId"}]},{name:"setChildNodes",description:"Fired when backend wants to provide client with the missing DOM structure. This happens upon\nmost of the calls requesting node ids.",parameters:[{name:"parentId",description:"Parent node id to populate with children.",$ref:"NodeId"},{name:"nodes",description:"Child nodes array.",type:"array",items:{$ref:"Node"}}]},{name:"shadowRootPopped",description:"Called when shadow root is popped from the element.",experimental:!0,parameters:[{name:"hostId",description:"Host element id.",$ref:"NodeId"},{name:"rootId",description:"Shadow root id.",$ref:"NodeId"}]},{name:"shadowRootPushed",description:"Called when shadow root is pushed into the element.",experimental:!0,parameters:[{name:"hostId",description:"Host element id.",$ref:"NodeId"},{name:"root",description:"Shadow root.",$ref:"Node"}]}]},{domain:"DOMDebugger",description:"DOM debugging allows setting breakpoints on particular DOM operations and events. JavaScript\nexecution will stop on these operations as if there was a regular breakpoint set.",dependencies:["DOM","Debugger"],types:[{id:"DOMBreakpointType",description:"DOM breakpoint type.",type:"string",enum:["subtree-modified","attribute-modified","node-removed"]},{id:"EventListener",description:"Object event listener.",type:"object",properties:[{name:"type",description:"`EventListener`'s type.",type:"string"},{name:"useCapture",description:"`EventListener`'s useCapture.",type:"boolean"},{name:"passive",description:"`EventListener`'s passive flag.",type:"boolean"},{name:"once",description:"`EventListener`'s once flag.",type:"boolean"},{name:"scriptId",description:"Script id of the handler code.",$ref:"Runtime.ScriptId"},{name:"lineNumber",description:"Line number in the script (0-based).",type:"integer"},{name:"columnNumber",description:"Column number in the script (0-based).",type:"integer"},{name:"handler",description:"Event handler function value.",optional:!0,$ref:"Runtime.RemoteObject"},{name:"originalHandler",description:"Event original handler function value.",optional:!0,$ref:"Runtime.RemoteObject"},{name:"backendNodeId",description:"Node the listener is added to (if any).",optional:!0,$ref:"DOM.BackendNodeId"}]}],commands:[{name:"getEventListeners",description:"Returns event listeners of the given object.",parameters:[{name:"objectId",description:"Identifier of the object to return listeners for.",$ref:"Runtime.RemoteObjectId"},{name:"depth",description:"The maximum depth at which Node children should be retrieved, defaults to 1. Use -1 for the\nentire subtree or provide an integer larger than 0.",optional:!0,type:"integer"},{name:"pierce",description:"Whether or not iframes and shadow roots should be traversed when returning the subtree\n(default is false). Reports listeners for all contexts if pierce is enabled.",optional:!0,type:"boolean"}],returns:[{name:"listeners",description:"Array of relevant listeners.",type:"array",items:{$ref:"EventListener"}}]},{name:"removeDOMBreakpoint",description:"Removes DOM breakpoint that was set using `setDOMBreakpoint`.",parameters:[{name:"nodeId",description:"Identifier of the node to remove breakpoint from.",$ref:"DOM.NodeId"},{name:"type",description:"Type of the breakpoint to remove.",$ref:"DOMBreakpointType"}]},{name:"removeEventListenerBreakpoint",description:"Removes breakpoint on particular DOM event.",parameters:[{name:"eventName",description:"Event name.",type:"string"},{name:"targetName",description:"EventTarget interface name.",experimental:!0,optional:!0,type:"string"}]},{name:"removeInstrumentationBreakpoint",description:"Removes breakpoint on particular native event.",experimental:!0,parameters:[{name:"eventName",description:"Instrumentation name to stop on.",type:"string"}]},{name:"removeXHRBreakpoint",description:"Removes breakpoint from XMLHttpRequest.",parameters:[{name:"url",description:"Resource URL substring.",type:"string"}]},{name:"setDOMBreakpoint",description:"Sets breakpoint on particular operation with DOM.",parameters:[{name:"nodeId",description:"Identifier of the node to set breakpoint on.",$ref:"DOM.NodeId"},{name:"type",description:"Type of the operation to stop upon.",$ref:"DOMBreakpointType"}]},{name:"setEventListenerBreakpoint",description:"Sets breakpoint on particular DOM event.",parameters:[{name:"eventName",description:"DOM Event name to stop on (any DOM event will do).",type:"string"},{name:"targetName",description:'EventTarget interface name to stop on. If equal to `"*"` or not provided, will stop on any\nEventTarget.',experimental:!0,optional:!0,type:"string"}]},{name:"setInstrumentationBreakpoint",description:"Sets breakpoint on particular native event.",experimental:!0,parameters:[{name:"eventName",description:"Instrumentation name to stop on.",type:"string"}]},{name:"setXHRBreakpoint",description:"Sets breakpoint on XMLHttpRequest.",parameters:[{name:"url",description:"Resource URL substring. All XHRs having this substring in the URL will get stopped upon.",type:"string"}]}]},{domain:"DOMSnapshot",description:"This domain facilitates obtaining document snapshots with DOM, layout, and style information.",experimental:!0,dependencies:["CSS","DOM","DOMDebugger","Page"],types:[{id:"DOMNode",description:"A Node in the DOM tree.",type:"object",properties:[{name:"nodeType",description:"`Node`'s nodeType.",type:"integer"},{name:"nodeName",description:"`Node`'s nodeName.",type:"string"},{name:"nodeValue",description:"`Node`'s nodeValue.",type:"string"},{name:"textValue",description:"Only set for textarea elements, contains the text value.",optional:!0,type:"string"},{name:"inputValue",description:"Only set for input elements, contains the input's associated text value.",optional:!0,type:"string"},{name:"inputChecked",description:"Only set for radio and checkbox input elements, indicates if the element has been checked",optional:!0,type:"boolean"},{name:"optionSelected",description:"Only set for option elements, indicates if the element has been selected",optional:!0,type:"boolean"},{name:"backendNodeId",description:"`Node`'s id, corresponds to DOM.Node.backendNodeId.",$ref:"DOM.BackendNodeId"},{name:"childNodeIndexes",description:"The indexes of the node's child nodes in the `domNodes` array returned by `getSnapshot`, if\nany.",optional:!0,type:"array",items:{type:"integer"}},{name:"attributes",description:"Attributes of an `Element` node.",optional:!0,type:"array",items:{$ref:"NameValue"}},{name:"pseudoElementIndexes",description:"Indexes of pseudo elements associated with this node in the `domNodes` array returned by\n`getSnapshot`, if any.",optional:!0,type:"array",items:{type:"integer"}},{name:"layoutNodeIndex",description:"The index of the node's related layout tree node in the `layoutTreeNodes` array returned by\n`getSnapshot`, if any.",optional:!0,type:"integer"},{name:"documentURL",description:"Document URL that `Document` or `FrameOwner` node points to.",optional:!0,type:"string"},{name:"baseURL",description:"Base URL that `Document` or `FrameOwner` node uses for URL completion.",optional:!0,type:"string"},{name:"contentLanguage",description:"Only set for documents, contains the document's content language.",optional:!0,type:"string"},{name:"documentEncoding",description:"Only set for documents, contains the document's character set encoding.",optional:!0,type:"string"},{name:"publicId",description:"`DocumentType` node's publicId.",optional:!0,type:"string"},{name:"systemId",description:"`DocumentType` node's systemId.",optional:!0,type:"string"},{name:"frameId",description:"Frame ID for frame owner elements and also for the document node.",optional:!0,$ref:"Page.FrameId"},{name:"contentDocumentIndex",description:"The index of a frame owner element's content document in the `domNodes` array returned by\n`getSnapshot`, if any.",optional:!0,type:"integer"},{name:"importedDocumentIndex",description:"Index of the imported document's node of a link element in the `domNodes` array returned by\n`getSnapshot`, if any.",optional:!0,type:"integer"},{name:"templateContentIndex",description:"Index of the content node of a template element in the `domNodes` array returned by\n`getSnapshot`.",optional:!0,type:"integer"},{name:"pseudoType",description:"Type of a pseudo element node.",optional:!0,$ref:"DOM.PseudoType"},{name:"shadowRootType",description:"Shadow root type.",optional:!0,$ref:"DOM.ShadowRootType"},{name:"isClickable",description:"Whether this DOM node responds to mouse clicks. This includes nodes that have had click\nevent listeners attached via JavaScript as well as anchor tags that naturally navigate when\nclicked.",optional:!0,type:"boolean"},{name:"eventListeners",description:"Details of the node's event listeners, if any.",optional:!0,type:"array",items:{$ref:"DOMDebugger.EventListener"}},{name:"currentSourceURL",description:"The selected url for nodes with a srcset attribute.",optional:!0,type:"string"},{name:"originURL",description:"The url of the script (if any) that generates this node.",optional:!0,type:"string"}]},{id:"InlineTextBox",description:"Details of post layout rendered text positions. The exact layout should not be regarded as\nstable and may change between versions.",type:"object",properties:[{
name:"boundingBox",description:"The absolute position bounding box.",$ref:"DOM.Rect"},{name:"startCharacterIndex",description:"The starting index in characters, for this post layout textbox substring. Characters that\nwould be represented as a surrogate pair in UTF-16 have length 2.",type:"integer"},{name:"numCharacters",description:"The number of characters in this post layout textbox substring. Characters that would be\nrepresented as a surrogate pair in UTF-16 have length 2.",type:"integer"}]},{id:"LayoutTreeNode",description:"Details of an element in the DOM tree with a LayoutObject.",type:"object",properties:[{name:"domNodeIndex",description:"The index of the related DOM node in the `domNodes` array returned by `getSnapshot`.",type:"integer"},{name:"boundingBox",description:"The absolute position bounding box.",$ref:"DOM.Rect"},{name:"layoutText",description:"Contents of the LayoutText, if any.",optional:!0,type:"string"},{name:"inlineTextNodes",description:"The post-layout inline text nodes, if any.",optional:!0,type:"array",items:{$ref:"InlineTextBox"}},{name:"styleIndex",description:"Index into the `computedStyles` array returned by `getSnapshot`.",optional:!0,type:"integer"},{name:"paintOrder",description:"Global paint order index, which is determined by the stacking order of the nodes. Nodes\nthat are painted together will have the same index. Only provided if includePaintOrder in\ngetSnapshot was true.",optional:!0,type:"integer"}]},{id:"ComputedStyle",description:"A subset of the full ComputedStyle as defined by the request whitelist.",type:"object",properties:[{name:"properties",description:"Name/value pairs of computed style properties.",type:"array",items:{$ref:"NameValue"}}]},{id:"NameValue",description:"A name/value pair.",type:"object",properties:[{name:"name",description:"Attribute/property name.",type:"string"},{name:"value",description:"Attribute/property value.",type:"string"}]},{id:"StringIndex",description:"Index of the string in the strings table.",type:"integer"},{id:"ArrayOfStrings",description:"Index of the string in the strings table.",type:"array",items:{$ref:"StringIndex"}},{id:"RareStringData",description:"Data that is only present on rare nodes.",type:"object",properties:[{name:"index",type:"array",items:{type:"integer"}},{name:"value",type:"array",items:{$ref:"StringIndex"}}]},{id:"RareBooleanData",type:"object",properties:[{name:"index",type:"array",items:{type:"integer"}}]},{id:"RareIntegerData",type:"object",properties:[{name:"index",type:"array",items:{type:"integer"}},{name:"value",type:"array",items:{type:"integer"}}]},{id:"Rectangle",type:"array",items:{type:"number"}},{id:"DOMTreeSnapshot",description:"DOM tree snapshot.",type:"object",properties:[{name:"parentIndex",description:"Parent node index.",optional:!0,type:"array",items:{type:"integer"}},{name:"nodeType",description:"`Node`'s nodeType.",optional:!0,type:"array",items:{type:"integer"}},{name:"nodeName",description:"`Node`'s nodeName.",optional:!0,type:"array",items:{$ref:"StringIndex"}},{name:"nodeValue",description:"`Node`'s nodeValue.",optional:!0,type:"array",items:{$ref:"StringIndex"}},{name:"backendNodeId",description:"`Node`'s id, corresponds to DOM.Node.backendNodeId.",optional:!0,type:"array",items:{$ref:"DOM.BackendNodeId"}},{name:"attributes",description:"Attributes of an `Element` node. Flatten name, value pairs.",optional:!0,type:"array",items:{$ref:"ArrayOfStrings"}},{name:"layoutNodeIndex",description:"The index of the node's related layout tree node in the `layoutTreeNodes` array returned by\n`captureSnapshot`, if any.",optional:!0,type:"array",items:{type:"integer"}},{name:"textValue",description:"Only set for textarea elements, contains the text value.",optional:!0,$ref:"RareStringData"},{name:"inputValue",description:"Only set for input elements, contains the input's associated text value.",optional:!0,$ref:"RareStringData"},{name:"inputChecked",description:"Only set for radio and checkbox input elements, indicates if the element has been checked",optional:!0,$ref:"RareBooleanData"},{name:"optionSelected",description:"Only set for option elements, indicates if the element has been selected",optional:!0,$ref:"RareBooleanData"},{name:"documentURL",description:"Document URL that `Document` or `FrameOwner` node points to.",optional:!0,$ref:"RareStringData"},{name:"baseURL",description:"Base URL that `Document` or `FrameOwner` node uses for URL completion.",optional:!0,$ref:"RareStringData"},{name:"contentLanguage",description:"Only set for documents, contains the document's content language.",optional:!0,$ref:"RareStringData"},{name:"documentEncoding",description:"Only set for documents, contains the document's character set encoding.",optional:!0,$ref:"RareStringData"},{name:"publicId",description:"`DocumentType` node's publicId.",optional:!0,$ref:"RareStringData"},{name:"systemId",description:"`DocumentType` node's systemId.",optional:!0,$ref:"RareStringData"},{name:"frameId",description:"Frame ID for frame owner elements and also for the document node.",optional:!0,$ref:"RareStringData"},{name:"contentDocumentIndex",description:"The index of a frame owner element's content document in the `domNodes` array returned by\n`captureSnapshot`, if any.",optional:!0,$ref:"RareIntegerData"},{name:"importedDocumentIndex",description:"Index of the imported document's node of a link element in the `domNodes` array returned by\n`captureSnapshot`, if any.",optional:!0,$ref:"RareIntegerData"},{name:"templateContentIndex",description:"Index of the content node of a template element in the `domNodes` array returned by\n`captureSnapshot`.",optional:!0,$ref:"RareIntegerData"},{name:"pseudoType",description:"Type of a pseudo element node.",optional:!0,$ref:"RareStringData"},{name:"isClickable",description:"Whether this DOM node responds to mouse clicks. This includes nodes that have had click\nevent listeners attached via JavaScript as well as anchor tags that naturally navigate when\nclicked.",optional:!0,$ref:"RareBooleanData"},{name:"currentSourceURL",description:"The selected url for nodes with a srcset attribute.",optional:!0,$ref:"RareStringData"},{name:"originURL",description:"The url of the script (if any) that generates this node.",optional:!0,$ref:"RareStringData"}]},{id:"TextBoxSnapshot",description:"Details of post layout rendered text positions. The exact layout should not be regarded as\nstable and may change between versions.",type:"object",properties:[{name:"layoutIndex",description:"Intex of th elayout tree node that owns this box collection.",type:"array",items:{type:"integer"}},{name:"bounds",description:"The absolute position bounding box.",type:"array",items:{$ref:"Rectangle"}},{name:"start",description:"The starting index in characters, for this post layout textbox substring. Characters that\nwould be represented as a surrogate pair in UTF-16 have length 2.",type:"array",items:{type:"integer"}},{name:"length",description:"The number of characters in this post layout textbox substring. Characters that would be\nrepresented as a surrogate pair in UTF-16 have length 2.",type:"array",items:{type:"integer"}}]},{id:"LayoutTreeSnapshot",description:"Details of an element in the DOM tree with a LayoutObject.",type:"object",properties:[{name:"nodeIndex",description:"The index of the related DOM node in the `domNodes` array returned by `getSnapshot`.",type:"array",items:{type:"integer"}},{name:"styles",description:"Index into the `computedStyles` array returned by `captureSnapshot`.",type:"array",items:{$ref:"ArrayOfStrings"}},{name:"bounds",description:"The absolute position bounding box.",type:"array",items:{$ref:"Rectangle"}},{name:"text",description:"Contents of the LayoutText, if any.",type:"array",items:{$ref:"StringIndex"}},{name:"textBoxes",description:"The post-layout inline text nodes",$ref:"TextBoxSnapshot"}]},{id:"StylesSnapshot",description:"Computed style snapshot.",type:"object",properties:[{name:"values",description:"Whitelisted ComputedStyle property values referenced by styleIndex.",type:"array",items:{$ref:"ArrayOfStrings"}}]}],commands:[{name:"disable",description:"Disables DOM snapshot agent for the given page."},{name:"enable",description:"Enables DOM snapshot agent for the given page."},{name:"getSnapshot",description:"Returns a document snapshot, including the full DOM tree of the root node (including iframes,\ntemplate contents, and imported documents) in a flattened array, as well as layout and\nwhite-listed computed style information for the nodes. Shadow DOM in the returned DOM tree is\nflattened.",deprecated:!0,parameters:[{name:"computedStyleWhitelist",description:"Whitelist of computed styles to return.",type:"array",items:{type:"string"}},{name:"includeEventListeners",description:"Whether or not to retrieve details of DOM listeners (default false).",optional:!0,type:"boolean"},{name:"includePaintOrder",description:"Whether to determine and include the paint order index of LayoutTreeNodes (default false).",optional:!0,type:"boolean"},{name:"includeUserAgentShadowTree",description:"Whether to include UA shadow tree in the snapshot (default false).",optional:!0,type:"boolean"}],returns:[{name:"domNodes",description:"The nodes in the DOM tree. The DOMNode at index 0 corresponds to the root document.",type:"array",items:{$ref:"DOMNode"}},{name:"layoutTreeNodes",description:"The nodes in the layout tree.",type:"array",items:{$ref:"LayoutTreeNode"}},{name:"computedStyles",description:"Whitelisted ComputedStyle properties for each node in the layout tree.",type:"array",items:{$ref:"ComputedStyle"}}]},{name:"captureSnapshot",description:"Returns a document snapshot, including the full DOM tree of the root node (including iframes,\ntemplate contents, and imported documents) in a flattened array, as well as layout and\nwhite-listed computed style information for the nodes. Shadow DOM in the returned DOM tree is\nflattened.",parameters:[{name:"computedStyles",description:"Whitelist of computed styles to return.",type:"array",items:{type:"string"}}],returns:[{name:"nodes",description:"The nodes in the DOM tree. The DOMNode at index 0 corresponds to the root document.",$ref:"DOMTreeSnapshot"},{name:"layout",description:"The nodes in the layout tree.",$ref:"LayoutTreeSnapshot"},{name:"strings",description:"Shared string table that all string properties refer to with indexes.",type:"array",items:{type:"string"}}]}]},{domain:"DOMStorage",description:"Query and modify DOM storage.",experimental:!0,types:[{id:"StorageId",description:"DOM Storage identifier.",type:"object",properties:[{name:"securityOrigin",description:"Security origin for the storage.",type:"string"},{name:"isLocalStorage",description:"Whether the storage is local storage (not session storage).",type:"boolean"}]},{id:"Item",description:"DOM Storage item.",type:"array",items:{type:"string"}}],commands:[{name:"clear",parameters:[{name:"storageId",$ref:"StorageId"}]},{name:"disable",description:"Disables storage tracking, prevents storage events from being sent to the client."},{name:"enable",description:"Enables storage tracking, storage events will now be delivered to the client."},{name:"getDOMStorageItems",parameters:[{name:"storageId",$ref:"StorageId"}],returns:[{name:"entries",type:"array",items:{$ref:"Item"}}]},{name:"removeDOMStorageItem",parameters:[{name:"storageId",$ref:"StorageId"},{name:"key",type:"string"}]},{name:"setDOMStorageItem",parameters:[{name:"storageId",$ref:"StorageId"},{name:"key",type:"string"},{name:"value",type:"string"}]}],events:[{name:"domStorageItemAdded",parameters:[{name:"storageId",$ref:"StorageId"},{name:"key",type:"string"},{name:"newValue",type:"string"}]},{name:"domStorageItemRemoved",parameters:[{name:"storageId",$ref:"StorageId"},{name:"key",type:"string"}]},{name:"domStorageItemUpdated",parameters:[{name:"storageId",$ref:"StorageId"},{name:"key",type:"string"},{name:"oldValue",type:"string"},{name:"newValue",type:"string"}]},{name:"domStorageItemsCleared",parameters:[{name:"storageId",$ref:"StorageId"}]}]},{domain:"Database",experimental:!0,types:[{id:"DatabaseId",description:"Unique identifier of Database object.",type:"string"},{id:"Database",description:"Database object.",type:"object",properties:[{name:"id",description:"Database ID.",$ref:"DatabaseId"},{name:"domain",description:"Database domain.",type:"string"},{name:"name",description:"Database name.",type:"string"},{name:"version",description:"Database version.",type:"string"}]},{id:"Error",description:"Database error.",type:"object",properties:[{name:"message",description:"Error message.",type:"string"},{name:"code",description:"Error code.",type:"integer"}]}],commands:[{name:"disable",description:"Disables database tracking, prevents database events from being sent to the client."},{name:"enable",description:"Enables database tracking, database events will now be delivered to the client."},{name:"executeSQL",parameters:[{name:"databaseId",$ref:"DatabaseId"},{name:"query",type:"string"}],returns:[{name:"columnNames",optional:!0,type:"array",items:{type:"string"}},{name:"values",optional:!0,type:"array",items:{type:"any"}},{name:"sqlError",optional:!0,$ref:"Error"}]},{name:"getDatabaseTableNames",parameters:[{name:"databaseId",$ref:"DatabaseId"}],returns:[{name:"tableNames",type:"array",items:{type:"string"}}]}],events:[{name:"addDatabase",parameters:[{name:"database",$ref:"Database"}]}]},{domain:"DeviceOrientation",experimental:!0,commands:[{name:"clearDeviceOrientationOverride",description:"Clears the overridden Device Orientation."},{name:"setDeviceOrientationOverride",description:"Overrides the Device Orientation.",parameters:[{name:"alpha",description:"Mock alpha",type:"number"},{name:"beta",description:"Mock beta",type:"number"},{name:"gamma",description:"Mock gamma",type:"number"}]}]},{domain:"Emulation",description:"This domain emulates different environments for the page.",dependencies:["DOM","Page","Runtime"],types:[{id:"ScreenOrientation",description:"Screen orientation.",type:"object",properties:[{name:"type",description:"Orientation type.",type:"string",enum:["portraitPrimary","portraitSecondary","landscapePrimary","landscapeSecondary"]},{name:"angle",description:"Orientation angle.",type:"integer"}]},{id:"VirtualTimePolicy",description:"advance: If the scheduler runs out of immediate work, the virtual time base may fast forward to\nallow the next delayed task (if any) to run; pause: The virtual time base may not advance;\npauseIfNetworkFetchesPending: The virtual time base may not advance if there are any pending\nresource fetches.",experimental:!0,type:"string",enum:["advance","pause","pauseIfNetworkFetchesPending"]}],commands:[{name:"canEmulate",description:"Tells whether emulation is supported.",returns:[{name:"result",description:"True if emulation is supported.",type:"boolean"}]},{name:"clearDeviceMetricsOverride",description:"Clears the overriden device metrics."},{name:"clearGeolocationOverride",description:"Clears the overriden Geolocation Position and Error."},{name:"resetPageScaleFactor",description:"Requests that page scale factor is reset to initial values.",experimental:!0},{name:"setCPUThrottlingRate",description:"Enables CPU throttling to emulate slow CPUs.",experimental:!0,parameters:[{name:"rate",description:"Throttling rate as a slowdown factor (1 is no throttle, 2 is 2x slowdown, etc).",type:"number"}]},{name:"setDefaultBackgroundColorOverride",description:"Sets or clears an override of the default background color of the frame. This override is used\nif the content does not specify one.",parameters:[{name:"color",description:"RGBA of the default background color. If not specified, any existing override will be\ncleared.",optional:!0,$ref:"DOM.RGBA"}]},{name:"setDeviceMetricsOverride",description:'Overrides the values of device screen dimensions (window.screen.width, window.screen.height,\nwindow.innerWidth, window.innerHeight, and "device-width"/"device-height"-related CSS media\nquery results).',parameters:[{name:"width",description:"Overriding width value in pixels (minimum 0, maximum 10000000). 0 disables the override.",type:"integer"},{name:"height",description:"Overriding height value in pixels (minimum 0, maximum 10000000). 0 disables the override.",type:"integer"},{name:"deviceScaleFactor",description:"Overriding device scale factor value. 0 disables the override.",type:"number"},{name:"mobile",description:"Whether to emulate mobile device. This includes viewport meta tag, overlay scrollbars, text\nautosizing and more.",type:"boolean"},{name:"scale",description:"Scale to apply to resulting view image.",experimental:!0,optional:!0,type:"number"},{name:"screenWidth",description:"Overriding screen width value in pixels (minimum 0, maximum 10000000).",experimental:!0,optional:!0,type:"integer"},{name:"screenHeight",description:"Overriding screen height value in pixels (minimum 0, maximum 10000000).",experimental:!0,optional:!0,type:"integer"},{name:"positionX",description:"Overriding view X position on screen in pixels (minimum 0, maximum 10000000).",experimental:!0,optional:!0,type:"integer"},{name:"positionY",description:"Overriding view Y position on screen in pixels (minimum 0, maximum 10000000).",experimental:!0,optional:!0,type:"integer"},{name:"dontSetVisibleSize",description:"Do not set visible view size, rely upon explicit setVisibleSize call.",experimental:!0,optional:!0,type:"boolean"},{name:"screenOrientation",description:"Screen orientation override.",optional:!0,$ref:"ScreenOrientation"},{name:"viewport",description:"If set, the visible area of the page will be overridden to this viewport. This viewport\nchange is not observed by the page, e.g. viewport-relative elements do not change positions.",experimental:!0,optional:!0,$ref:"Page.Viewport"}]},{name:"setScrollbarsHidden",experimental:!0,parameters:[{name:"hidden",description:"Whether scrollbars should be always hidden.",type:"boolean"}]},{name:"setDocumentCookieDisabled",experimental:!0,parameters:[{name:"disabled",description:"Whether document.coookie API should be disabled.",type:"boolean"}]},{name:"setEmitTouchEventsForMouse",experimental:!0,parameters:[{name:"enabled",description:"Whether touch emulation based on mouse input should be enabled.",type:"boolean"},{name:"configuration",description:"Touch/gesture events configuration. Default: current platform.",optional:!0,type:"string",enum:["mobile","desktop"]}]},{name:"setEmulatedMedia",description:"Emulates the given media for CSS media queries.",parameters:[{name:"media",description:"Media type to emulate. Empty string disables the override.",type:"string"}]},{name:"setGeolocationOverride",description:"Overrides the Geolocation Position or Error. Omitting any of the parameters emulates position\nunavailable.",parameters:[{name:"latitude",description:"Mock latitude",optional:!0,type:"number"},{name:"longitude",description:"Mock longitude",optional:!0,type:"number"},{name:"accuracy",description:"Mock accuracy",optional:!0,type:"number"}]},{name:"setNavigatorOverrides",description:"Overrides value returned by the javascript navigator object.",experimental:!0,deprecated:!0,parameters:[{name:"platform",description:"The platform navigator.platform should return.",type:"string"}]},{name:"setPageScaleFactor",description:"Sets a specified page scale factor.",experimental:!0,parameters:[{name:"pageScaleFactor",description:"Page scale factor.",type:"number"}]},{name:"setScriptExecutionDisabled",description:"Switches script execution in the page.",parameters:[{name:"value",description:"Whether script execution should be disabled in the page.",type:"boolean"}]},{name:"setTouchEmulationEnabled",description:"Enables touch on platforms which do not support them.",parameters:[{name:"enabled",description:"Whether the touch event emulation should be enabled.",type:"boolean"},{name:"maxTouchPoints",description:"Maximum touch points supported. Defaults to one.",optional:!0,type:"integer"}]},{name:"setVirtualTimePolicy",description:"Turns on virtual time for all frames (replacing real-time with a synthetic time source) and sets\nthe current virtual time policy.  Note this supersedes any previous time budget.",experimental:!0,parameters:[{name:"policy",$ref:"VirtualTimePolicy"},{name:"budget",description:"If set, after this many virtual milliseconds have elapsed virtual time will be paused and a\nvirtualTimeBudgetExpired event is sent.",optional:!0,type:"number"},{name:"maxVirtualTimeTaskStarvationCount",description:"If set this specifies the maximum number of tasks that can be run before virtual is forced\nforwards to prevent deadlock.",optional:!0,type:"integer"},{name:"waitForNavigation",description:"If set the virtual time policy change should be deferred until any frame starts navigating.\nNote any previous deferred policy change is superseded.",optional:!0,type:"boolean"},{name:"initialVirtualTime",description:"If set, base::Time::Now will be overriden to initially return this value.",optional:!0,$ref:"Network.TimeSinceEpoch"}],returns:[{name:"virtualTimeTicksBase",description:"Absolute timestamp at which virtual time was first enabled (up time in milliseconds).",type:"number"}]},{name:"setVisibleSize",description:"Resizes the frame/viewport of the page. Note that this does not affect the frame's container\n(e.g. browser window). Can be used to produce screenshots of the specified size. Not supported\non Android.",experimental:!0,deprecated:!0,parameters:[{name:"width",description:"Frame width (DIP).",type:"integer"},{name:"height",description:"Frame height (DIP).",type:"integer"}]},{name:"setUserAgentOverride",description:"Allows overriding user agent with the given string.",parameters:[{name:"userAgent",description:"User agent to use.",type:"string"},{name:"acceptLanguage",description:"Browser langugage to emulate.",optional:!0,type:"string"},{name:"platform",description:"The platform navigator.platform should return.",optional:!0,type:"string"}]}],events:[{name:"virtualTimeAdvanced",description:"Notification sent after the virtual time has advanced.",experimental:!0,parameters:[{name:"virtualTimeElapsed",description:"The amount of virtual time that has elapsed in milliseconds since virtual time was first\nenabled.",type:"number"}]},{name:"virtualTimeBudgetExpired",description:"Notification sent after the virtual time budget for the current VirtualTimePolicy has run out.",experimental:!0},{name:"virtualTimePaused",description:"Notification sent after the virtual time has paused.",experimental:!0,parameters:[{name:"virtualTimeElapsed",description:"The amount of virtual time that has elapsed in milliseconds since virtual time was first\nenabled.",type:"number"}]}]},{domain:"HeadlessExperimental",description:"This domain provides experimental commands only supported in headless mode.",experimental:!0,dependencies:["Page","Runtime"],types:[{id:"ScreenshotParams",description:"Encoding options for a screenshot.",type:"object",properties:[{name:"format",description:"Image compression format (defaults to png).",optional:!0,type:"string",enum:["jpeg","png"]},{name:"quality",description:"Compression quality from range [0..100] (jpeg only).",optional:!0,type:"integer"}]}],commands:[{name:"beginFrame",description:"Sends a BeginFrame to the target and returns when the frame was completed. Optionally captures a\nscreenshot from the resulting frame. Requires that the target was created with enabled\nBeginFrameControl. Designed for use with --run-all-compositor-stages-before-draw, see also\nhttps://goo.gl/3zHXhB for more background.",parameters:[{name:"frameTimeTicks",description:"Timestamp of this BeginFrame in Renderer TimeTicks (milliseconds of uptime). If not set,\nthe current time will be used.",optional:!0,type:"number"},{name:"interval",description:"The interval between BeginFrames that is reported to the compositor, in milliseconds.\nDefaults to a 60 frames/second interval, i.e. about 16.666 milliseconds.",optional:!0,type:"number"},{name:"noDisplayUpdates",description:"Whether updates should not be committed and drawn onto the display. False by default. If\ntrue, only side effects of the BeginFrame will be run, such as layout and animations, but\nany visual updates may not be visible on the display or in screenshots.",optional:!0,type:"boolean"},{name:"screenshot",description:"If set, a screenshot of the frame will be captured and returned in the response. Otherwise,\nno screenshot will be captured. Note that capturing a screenshot can fail, for example,\nduring renderer initialization. In such a case, no screenshot data will be returned.",optional:!0,$ref:"ScreenshotParams"}],returns:[{name:"hasDamage",description:"Whether the BeginFrame resulted in damage and, thus, a new frame was committed to the\ndisplay. Reported for diagnostic uses, may be removed in the future.",type:"boolean"},{name:"screenshotData",description:"Base64-encoded image data of the screenshot, if one was requested and successfully taken.",optional:!0,type:"string"}]},{name:"disable",description:"Disables headless events for the target."},{name:"enable",description:"Enables headless events for the target."}],events:[{name:"needsBeginFramesChanged",description:"Issued when the target starts or stops needing BeginFrames.",parameters:[{name:"needsBeginFrames",description:"True if BeginFrames are needed, false otherwise.",type:"boolean"}]}]},{domain:"IO",description:"Input/Output operations for streams produced by DevTools.",types:[{id:"StreamHandle",description:"This is either obtained from another method or specifed as `blob:&lt;uuid&gt;` where\n`&lt;uuid&gt` is an UUID of a Blob.",type:"string"}],commands:[{name:"close",description:"Close the stream, discard any temporary backing storage.",parameters:[{name:"handle",description:"Handle of the stream to close.",$ref:"StreamHandle"}]},{name:"read",description:"Read a chunk of the stream",parameters:[{name:"handle",description:"Handle of the stream to read.",$ref:"StreamHandle"},{name:"offset",description:"Seek to the specified offset before reading (if not specificed, proceed with offset\nfollowing the last read). Some types of streams may only support sequential reads.",optional:!0,type:"integer"},{name:"size",description:"Maximum number of bytes to read (left upon the agent discretion if not specified).",optional:!0,type:"integer"}],returns:[{name:"base64Encoded",description:"Set if the data is base64-encoded",optional:!0,type:"boolean"},{name:"data",description:"Data that were read.",type:"string"},{name:"eof",description:"Set if the end-of-file condition occured while reading.",type:"boolean"}]},{name:"resolveBlob",description:"Return UUID of Blob object specified by a remote object id.",parameters:[{name:"objectId",description:"Object id of a Blob object wrapper.",$ref:"Runtime.RemoteObjectId"}],returns:[{name:"uuid",description:"UUID of the specified Blob.",type:"string"}]}]},{domain:"IndexedDB",experimental:!0,dependencies:["Runtime"],types:[{id:"DatabaseWithObjectStores",description:"Database with an array of object stores.",type:"object",properties:[{name:"name",description:"Database name.",type:"string"},{name:"version",description:"Database version.",type:"integer"},{name:"objectStores",description:"Object stores in this database.",type:"array",items:{$ref:"ObjectStore"}}]},{id:"ObjectStore",description:"Object store.",type:"object",properties:[{name:"name",description:"Object store name.",type:"string"},{name:"keyPath",description:"Object store key path.",$ref:"KeyPath"},{name:"autoIncrement",description:"If true, object store has auto increment flag set.",type:"boolean"},{name:"indexes",description:"Indexes in this object store.",type:"array",items:{$ref:"ObjectStoreIndex"}}]},{id:"ObjectStoreIndex",description:"Object store index.",type:"object",properties:[{name:"name",description:"Index name.",type:"string"},{name:"keyPath",description:"Index key path.",$ref:"KeyPath"},{name:"unique",description:"If true, index is unique.",type:"boolean"},{name:"multiEntry",description:"If true, index allows multiple entries for a key.",type:"boolean"}]},{id:"Key",description:"Key.",type:"object",properties:[{name:"type",description:"Key type.",type:"string",enum:["number","string","date","array"]},{name:"number",description:"Number value.",optional:!0,type:"number"},{name:"string",description:"String value.",optional:!0,type:"string"},{name:"date",description:"Date value.",optional:!0,type:"number"},{name:"array",description:"Array value.",optional:!0,type:"array",items:{$ref:"Key"}}]},{id:"KeyRange",description:"Key range.",type:"object",properties:[{name:"lower",description:"Lower bound.",optional:!0,$ref:"Key"},{name:"upper",description:"Upper bound.",optional:!0,$ref:"Key"},{name:"lowerOpen",description:"If true lower bound is open.",type:"boolean"},{name:"upperOpen",description:"If true upper bound is open.",type:"boolean"}]},{id:"DataEntry",description:"Data entry.",type:"object",properties:[{name:"key",description:"Key object.",$ref:"Runtime.RemoteObject"},{name:"primaryKey",description:"Primary key object.",$ref:"Runtime.RemoteObject"},{name:"value",description:"Value object.",$ref:"Runtime.RemoteObject"}]},{id:"KeyPath",description:"Key path.",type:"object",properties:[{name:"type",description:"Key path type.",type:"string",enum:["null","string","array"]},{name:"string",description:"String value.",optional:!0,type:"string"},{name:"array",description:"Array value.",optional:!0,type:"array",items:{type:"string"}}]}],commands:[{name:"clearObjectStore",description:"Clears all entries from an object store.",parameters:[{name:"securityOrigin",description:"Security origin.",type:"string"},{name:"databaseName",description:"Database name.",type:"string"},{name:"objectStoreName",description:"Object store name.",type:"string"}]},{name:"deleteDatabase",description:"Deletes a database.",parameters:[{name:"securityOrigin",description:"Security origin.",type:"string"},{name:"databaseName",description:"Database name.",type:"string"}]},{name:"deleteObjectStoreEntries",description:"Delete a range of entries from an object store",parameters:[{name:"securityOrigin",type:"string"},{name:"databaseName",type:"string"},{name:"objectStoreName",type:"string"},{name:"keyRange",description:"Range of entry keys to delete",$ref:"KeyRange"}]},{name:"disable",description:"Disables events from backend."},{name:"enable",description:"Enables events from backend."},{name:"requestData",description:"Requests data from object store or index.",parameters:[{name:"securityOrigin",description:"Security origin.",type:"string"},{name:"databaseName",description:"Database name.",type:"string"},{name:"objectStoreName",description:"Object store name.",type:"string"},{name:"indexName",description:"Index name, empty string for object store data requests.",type:"string"},{name:"skipCount",description:"Number of records to skip.",type:"integer"},{name:"pageSize",description:"Number of records to fetch.",type:"integer"},{name:"keyRange",description:"Key range.",optional:!0,$ref:"KeyRange"}],returns:[{name:"objectStoreDataEntries",description:"Array of object store data entries.",type:"array",items:{$ref:"DataEntry"}},{name:"hasMore",description:"If true, there are more entries to fetch in the given range.",type:"boolean"}]},{name:"requestDatabase",description:"Requests database with given name in given frame.",parameters:[{name:"securityOrigin",description:"Security origin.",type:"string"},{name:"databaseName",description:"Database name.",type:"string"}],returns:[{name:"databaseWithObjectStores",description:"Database with an array of object stores.",$ref:"DatabaseWithObjectStores"}]},{name:"requestDatabaseNames",description:"Requests database names for given security origin.",parameters:[{name:"securityOrigin",description:"Security origin.",type:"string"}],returns:[{name:"databaseNames",description:"Database names for origin.",type:"array",items:{type:"string"}}]}]},{domain:"Input",types:[{
id:"TouchPoint",type:"object",properties:[{name:"x",description:"X coordinate of the event relative to the main frame's viewport in CSS pixels.",type:"number"},{name:"y",description:"Y coordinate of the event relative to the main frame's viewport in CSS pixels. 0 refers to\nthe top of the viewport and Y increases as it proceeds towards the bottom of the viewport.",type:"number"},{name:"radiusX",description:"X radius of the touch area (default: 1.0).",optional:!0,type:"number"},{name:"radiusY",description:"Y radius of the touch area (default: 1.0).",optional:!0,type:"number"},{name:"rotationAngle",description:"Rotation angle (default: 0.0).",optional:!0,type:"number"},{name:"force",description:"Force (default: 1.0).",optional:!0,type:"number"},{name:"id",description:"Identifier used to track touch sources between events, must be unique within an event.",optional:!0,type:"number"}]},{id:"GestureSourceType",experimental:!0,type:"string",enum:["default","touch","mouse"]},{id:"TimeSinceEpoch",description:"UTC time in seconds, counted from January 1, 1970.",type:"number"}],commands:[{name:"dispatchKeyEvent",description:"Dispatches a key event to the page.",parameters:[{name:"type",description:"Type of the key event.",type:"string",enum:["keyDown","keyUp","rawKeyDown","char"]},{name:"modifiers",description:"Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8\n(default: 0).",optional:!0,type:"integer"},{name:"timestamp",description:"Time at which the event occurred.",optional:!0,$ref:"TimeSinceEpoch"},{name:"text",description:'Text as generated by processing a virtual key code with a keyboard layout. Not needed for\nfor `keyUp` and `rawKeyDown` events (default: "")',optional:!0,type:"string"},{name:"unmodifiedText",description:'Text that would have been generated by the keyboard if no modifiers were pressed (except for\nshift). Useful for shortcut (accelerator) key handling (default: "").',optional:!0,type:"string"},{name:"keyIdentifier",description:"Unique key identifier (e.g., 'U+0041') (default: \"\").",optional:!0,type:"string"},{name:"code",description:"Unique DOM defined string value for each physical key (e.g., 'KeyA') (default: \"\").",optional:!0,type:"string"},{name:"key",description:"Unique DOM defined string value describing the meaning of the key in the context of active\nmodifiers, keyboard layout, etc (e.g., 'AltGr') (default: \"\").",optional:!0,type:"string"},{name:"windowsVirtualKeyCode",description:"Windows virtual key code (default: 0).",optional:!0,type:"integer"},{name:"nativeVirtualKeyCode",description:"Native virtual key code (default: 0).",optional:!0,type:"integer"},{name:"autoRepeat",description:"Whether the event was generated from auto repeat (default: false).",optional:!0,type:"boolean"},{name:"isKeypad",description:"Whether the event was generated from the keypad (default: false).",optional:!0,type:"boolean"},{name:"isSystemKey",description:"Whether the event was a system key event (default: false).",optional:!0,type:"boolean"},{name:"location",description:"Whether the event was from the left or right side of the keyboard. 1=Left, 2=Right (default:\n0).",optional:!0,type:"integer"}]},{name:"dispatchMouseEvent",description:"Dispatches a mouse event to the page.",parameters:[{name:"type",description:"Type of the mouse event.",type:"string",enum:["mousePressed","mouseReleased","mouseMoved","mouseWheel"]},{name:"x",description:"X coordinate of the event relative to the main frame's viewport in CSS pixels.",type:"number"},{name:"y",description:"Y coordinate of the event relative to the main frame's viewport in CSS pixels. 0 refers to\nthe top of the viewport and Y increases as it proceeds towards the bottom of the viewport.",type:"number"},{name:"modifiers",description:"Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8\n(default: 0).",optional:!0,type:"integer"},{name:"timestamp",description:"Time at which the event occurred.",optional:!0,$ref:"TimeSinceEpoch"},{name:"button",description:'Mouse button (default: "none").',optional:!0,type:"string",enum:["none","left","middle","right"]},{name:"clickCount",description:"Number of times the mouse button was clicked (default: 0).",optional:!0,type:"integer"},{name:"deltaX",description:"X delta in CSS pixels for mouse wheel event (default: 0).",optional:!0,type:"number"},{name:"deltaY",description:"Y delta in CSS pixels for mouse wheel event (default: 0).",optional:!0,type:"number"}]},{name:"dispatchTouchEvent",description:"Dispatches a touch event to the page.",parameters:[{name:"type",description:"Type of the touch event. TouchEnd and TouchCancel must not contain any touch points, while\nTouchStart and TouchMove must contains at least one.",type:"string",enum:["touchStart","touchEnd","touchMove","touchCancel"]},{name:"touchPoints",description:"Active touch points on the touch device. One event per any changed point (compared to\nprevious touch event in a sequence) is generated, emulating pressing/moving/releasing points\none by one.",type:"array",items:{$ref:"TouchPoint"}},{name:"modifiers",description:"Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8\n(default: 0).",optional:!0,type:"integer"},{name:"timestamp",description:"Time at which the event occurred.",optional:!0,$ref:"TimeSinceEpoch"}]},{name:"emulateTouchFromMouseEvent",description:"Emulates touch event from the mouse event parameters.",experimental:!0,parameters:[{name:"type",description:"Type of the mouse event.",type:"string",enum:["mousePressed","mouseReleased","mouseMoved","mouseWheel"]},{name:"x",description:"X coordinate of the mouse pointer in DIP.",type:"integer"},{name:"y",description:"Y coordinate of the mouse pointer in DIP.",type:"integer"},{name:"button",description:"Mouse button.",type:"string",enum:["none","left","middle","right"]},{name:"timestamp",description:"Time at which the event occurred (default: current time).",optional:!0,$ref:"TimeSinceEpoch"},{name:"deltaX",description:"X delta in DIP for mouse wheel event (default: 0).",optional:!0,type:"number"},{name:"deltaY",description:"Y delta in DIP for mouse wheel event (default: 0).",optional:!0,type:"number"},{name:"modifiers",description:"Bit field representing pressed modifier keys. Alt=1, Ctrl=2, Meta/Command=4, Shift=8\n(default: 0).",optional:!0,type:"integer"},{name:"clickCount",description:"Number of times the mouse button was clicked (default: 0).",optional:!0,type:"integer"}]},{name:"setIgnoreInputEvents",description:"Ignores input events (useful while auditing page).",parameters:[{name:"ignore",description:"Ignores input events processing when set to true.",type:"boolean"}]},{name:"synthesizePinchGesture",description:"Synthesizes a pinch gesture over a time period by issuing appropriate touch events.",experimental:!0,parameters:[{name:"x",description:"X coordinate of the start of the gesture in CSS pixels.",type:"number"},{name:"y",description:"Y coordinate of the start of the gesture in CSS pixels.",type:"number"},{name:"scaleFactor",description:"Relative scale factor after zooming (>1.0 zooms in, <1.0 zooms out).",type:"number"},{name:"relativeSpeed",description:"Relative pointer speed in pixels per second (default: 800).",optional:!0,type:"integer"},{name:"gestureSourceType",description:"Which type of input events to be generated (default: 'default', which queries the platform\nfor the preferred input type).",optional:!0,$ref:"GestureSourceType"}]},{name:"synthesizeScrollGesture",description:"Synthesizes a scroll gesture over a time period by issuing appropriate touch events.",experimental:!0,parameters:[{name:"x",description:"X coordinate of the start of the gesture in CSS pixels.",type:"number"},{name:"y",description:"Y coordinate of the start of the gesture in CSS pixels.",type:"number"},{name:"xDistance",description:"The distance to scroll along the X axis (positive to scroll left).",optional:!0,type:"number"},{name:"yDistance",description:"The distance to scroll along the Y axis (positive to scroll up).",optional:!0,type:"number"},{name:"xOverscroll",description:"The number of additional pixels to scroll back along the X axis, in addition to the given\ndistance.",optional:!0,type:"number"},{name:"yOverscroll",description:"The number of additional pixels to scroll back along the Y axis, in addition to the given\ndistance.",optional:!0,type:"number"},{name:"preventFling",description:"Prevent fling (default: true).",optional:!0,type:"boolean"},{name:"speed",description:"Swipe speed in pixels per second (default: 800).",optional:!0,type:"integer"},{name:"gestureSourceType",description:"Which type of input events to be generated (default: 'default', which queries the platform\nfor the preferred input type).",optional:!0,$ref:"GestureSourceType"},{name:"repeatCount",description:"The number of times to repeat the gesture (default: 0).",optional:!0,type:"integer"},{name:"repeatDelayMs",description:"The number of milliseconds delay between each repeat. (default: 250).",optional:!0,type:"integer"},{name:"interactionMarkerName",description:'The name of the interaction markers to generate, if not empty (default: "").',optional:!0,type:"string"}]},{name:"synthesizeTapGesture",description:"Synthesizes a tap gesture over a time period by issuing appropriate touch events.",experimental:!0,parameters:[{name:"x",description:"X coordinate of the start of the gesture in CSS pixels.",type:"number"},{name:"y",description:"Y coordinate of the start of the gesture in CSS pixels.",type:"number"},{name:"duration",description:"Duration between touchdown and touchup events in ms (default: 50).",optional:!0,type:"integer"},{name:"tapCount",description:"Number of times to perform the tap (e.g. 2 for double tap, default: 1).",optional:!0,type:"integer"},{name:"gestureSourceType",description:"Which type of input events to be generated (default: 'default', which queries the platform\nfor the preferred input type).",optional:!0,$ref:"GestureSourceType"}]}]},{domain:"Inspector",experimental:!0,commands:[{name:"disable",description:"Disables inspector domain notifications."},{name:"enable",description:"Enables inspector domain notifications."}],events:[{name:"detached",description:"Fired when remote debugging connection is about to be terminated. Contains detach reason.",parameters:[{name:"reason",description:"The reason why connection has been terminated.",type:"string"}]},{name:"targetCrashed",description:"Fired when debugging target has crashed"},{name:"targetReloadedAfterCrash",description:"Fired when debugging target has reloaded after crash"}]},{domain:"LayerTree",experimental:!0,dependencies:["DOM"],types:[{id:"LayerId",description:"Unique Layer identifier.",type:"string"},{id:"SnapshotId",description:"Unique snapshot identifier.",type:"string"},{id:"ScrollRect",description:"Rectangle where scrolling happens on the main thread.",type:"object",properties:[{name:"rect",description:"Rectangle itself.",$ref:"DOM.Rect"},{name:"type",description:"Reason for rectangle to force scrolling on the main thread",type:"string",enum:["RepaintsOnScroll","TouchEventHandler","WheelEventHandler"]}]},{id:"StickyPositionConstraint",description:"Sticky position constraints.",type:"object",properties:[{name:"stickyBoxRect",description:"Layout rectangle of the sticky element before being shifted",$ref:"DOM.Rect"},{name:"containingBlockRect",description:"Layout rectangle of the containing block of the sticky element",$ref:"DOM.Rect"},{name:"nearestLayerShiftingStickyBox",description:"The nearest sticky layer that shifts the sticky box",optional:!0,$ref:"LayerId"},{name:"nearestLayerShiftingContainingBlock",description:"The nearest sticky layer that shifts the containing block",optional:!0,$ref:"LayerId"}]},{id:"PictureTile",description:"Serialized fragment of layer picture along with its offset within the layer.",type:"object",properties:[{name:"x",description:"Offset from owning layer left boundary",type:"number"},{name:"y",description:"Offset from owning layer top boundary",type:"number"},{name:"picture",description:"Base64-encoded snapshot data.",type:"string"}]},{id:"Layer",description:"Information about a compositing layer.",type:"object",properties:[{name:"layerId",description:"The unique id for this layer.",$ref:"LayerId"},{name:"parentLayerId",description:"The id of parent (not present for root).",optional:!0,$ref:"LayerId"},{name:"backendNodeId",description:"The backend id for the node associated with this layer.",optional:!0,$ref:"DOM.BackendNodeId"},{name:"offsetX",description:"Offset from parent layer, X coordinate.",type:"number"},{name:"offsetY",description:"Offset from parent layer, Y coordinate.",type:"number"},{name:"width",description:"Layer width.",type:"number"},{name:"height",description:"Layer height.",type:"number"},{name:"transform",description:"Transformation matrix for layer, default is identity matrix",optional:!0,type:"array",items:{type:"number"}},{name:"anchorX",description:"Transform anchor point X, absent if no transform specified",optional:!0,type:"number"},{name:"anchorY",description:"Transform anchor point Y, absent if no transform specified",optional:!0,type:"number"},{name:"anchorZ",description:"Transform anchor point Z, absent if no transform specified",optional:!0,type:"number"},{name:"paintCount",description:"Indicates how many time this layer has painted.",type:"integer"},{name:"drawsContent",description:"Indicates whether this layer hosts any content, rather than being used for\ntransform/scrolling purposes only.",type:"boolean"},{name:"invisible",description:"Set if layer is not visible.",optional:!0,type:"boolean"},{name:"scrollRects",description:"Rectangles scrolling on main thread only.",optional:!0,type:"array",items:{$ref:"ScrollRect"}},{name:"stickyPositionConstraint",description:"Sticky position constraint information",optional:!0,$ref:"StickyPositionConstraint"}]},{id:"PaintProfile",description:"Array of timings, one per paint step.",type:"array",items:{type:"number"}}],commands:[{name:"compositingReasons",description:"Provides the reasons why the given layer was composited.",parameters:[{name:"layerId",description:"The id of the layer for which we want to get the reasons it was composited.",$ref:"LayerId"}],returns:[{name:"compositingReasons",description:"A list of strings specifying reasons for the given layer to become composited.",type:"array",items:{type:"string"}}]},{name:"disable",description:"Disables compositing tree inspection."},{name:"enable",description:"Enables compositing tree inspection."},{name:"loadSnapshot",description:"Returns the snapshot identifier.",parameters:[{name:"tiles",description:"An array of tiles composing the snapshot.",type:"array",items:{$ref:"PictureTile"}}],returns:[{name:"snapshotId",description:"The id of the snapshot.",$ref:"SnapshotId"}]},{name:"makeSnapshot",description:"Returns the layer snapshot identifier.",parameters:[{name:"layerId",description:"The id of the layer.",$ref:"LayerId"}],returns:[{name:"snapshotId",description:"The id of the layer snapshot.",$ref:"SnapshotId"}]},{name:"profileSnapshot",parameters:[{name:"snapshotId",description:"The id of the layer snapshot.",$ref:"SnapshotId"},{name:"minRepeatCount",description:"The maximum number of times to replay the snapshot (1, if not specified).",optional:!0,type:"integer"},{name:"minDuration",description:"The minimum duration (in seconds) to replay the snapshot.",optional:!0,type:"number"},{name:"clipRect",description:"The clip rectangle to apply when replaying the snapshot.",optional:!0,$ref:"DOM.Rect"}],returns:[{name:"timings",description:"The array of paint profiles, one per run.",type:"array",items:{$ref:"PaintProfile"}}]},{name:"releaseSnapshot",description:"Releases layer snapshot captured by the back-end.",parameters:[{name:"snapshotId",description:"The id of the layer snapshot.",$ref:"SnapshotId"}]},{name:"replaySnapshot",description:"Replays the layer snapshot and returns the resulting bitmap.",parameters:[{name:"snapshotId",description:"The id of the layer snapshot.",$ref:"SnapshotId"},{name:"fromStep",description:"The first step to replay from (replay from the very start if not specified).",optional:!0,type:"integer"},{name:"toStep",description:"The last step to replay to (replay till the end if not specified).",optional:!0,type:"integer"},{name:"scale",description:"The scale to apply while replaying (defaults to 1).",optional:!0,type:"number"}],returns:[{name:"dataURL",description:"A data: URL for resulting image.",type:"string"}]},{name:"snapshotCommandLog",description:"Replays the layer snapshot and returns canvas log.",parameters:[{name:"snapshotId",description:"The id of the layer snapshot.",$ref:"SnapshotId"}],returns:[{name:"commandLog",description:"The array of canvas function calls.",type:"array",items:{type:"object"}}]}],events:[{name:"layerPainted",parameters:[{name:"layerId",description:"The id of the painted layer.",$ref:"LayerId"},{name:"clip",description:"Clip rectangle.",$ref:"DOM.Rect"}]},{name:"layerTreeDidChange",parameters:[{name:"layers",description:"Layer tree, absent if not in the comspositing mode.",optional:!0,type:"array",items:{$ref:"Layer"}}]}]},{domain:"Log",description:"Provides access to log entries.",dependencies:["Runtime","Network"],types:[{id:"LogEntry",description:"Log entry.",type:"object",properties:[{name:"source",description:"Log entry source.",type:"string",enum:["xml","javascript","network","storage","appcache","rendering","security","deprecation","worker","violation","intervention","recommendation","other"]},{name:"level",description:"Log entry severity.",type:"string",enum:["verbose","info","warning","error"]},{name:"text",description:"Logged text.",type:"string"},{name:"timestamp",description:"Timestamp when this entry was added.",$ref:"Runtime.Timestamp"},{name:"url",description:"URL of the resource if known.",optional:!0,type:"string"},{name:"lineNumber",description:"Line number in the resource.",optional:!0,type:"integer"},{name:"stackTrace",description:"JavaScript stack trace.",optional:!0,$ref:"Runtime.StackTrace"},{name:"networkRequestId",description:"Identifier of the network request associated with this entry.",optional:!0,$ref:"Network.RequestId"},{name:"workerId",description:"Identifier of the worker associated with this entry.",optional:!0,type:"string"},{name:"args",description:"Call arguments.",optional:!0,type:"array",items:{$ref:"Runtime.RemoteObject"}}]},{id:"ViolationSetting",description:"Violation configuration setting.",type:"object",properties:[{name:"name",description:"Violation type.",type:"string",enum:["longTask","longLayout","blockedEvent","blockedParser","discouragedAPIUse","handler","recurringHandler"]},{name:"threshold",description:"Time threshold to trigger upon.",type:"number"}]}],commands:[{name:"clear",description:"Clears the log."},{name:"disable",description:"Disables log domain, prevents further log entries from being reported to the client."},{name:"enable",description:"Enables log domain, sends the entries collected so far to the client by means of the\n`entryAdded` notification."},{name:"startViolationsReport",description:"start violation reporting.",parameters:[{name:"config",description:"Configuration for violations.",type:"array",items:{$ref:"ViolationSetting"}}]},{name:"stopViolationsReport",description:"Stop violation reporting."}],events:[{name:"entryAdded",description:"Issued when new message was logged.",parameters:[{name:"entry",description:"The entry.",$ref:"LogEntry"}]}]},{domain:"Memory",experimental:!0,types:[{id:"PressureLevel",description:"Memory pressure level.",type:"string",enum:["moderate","critical"]},{id:"SamplingProfileNode",description:"Heap profile sample.",type:"object",properties:[{name:"size",description:"Size of the sampled allocation.",type:"number"},{name:"total",description:"Total bytes attributed to this sample.",type:"number"},{name:"stack",description:"Execution stack at the point of allocation.",type:"array",items:{type:"string"}}]},{id:"SamplingProfile",description:"Array of heap profile samples.",type:"object",properties:[{name:"samples",type:"array",items:{$ref:"SamplingProfileNode"}}]}],commands:[{name:"getDOMCounters",returns:[{name:"documents",type:"integer"},{name:"nodes",type:"integer"},{name:"jsEventListeners",type:"integer"}]},{name:"prepareForLeakDetection"},{name:"setPressureNotificationsSuppressed",description:"Enable/disable suppressing memory pressure notifications in all processes.",parameters:[{name:"suppressed",description:"If true, memory pressure notifications will be suppressed.",type:"boolean"}]},{name:"simulatePressureNotification",description:"Simulate a memory pressure notification in all processes.",parameters:[{name:"level",description:"Memory pressure level of the notification.",$ref:"PressureLevel"}]},{name:"startSampling",description:"Start collecting native memory profile.",parameters:[{name:"samplingInterval",description:"Average number of bytes between samples.",optional:!0,type:"integer"},{name:"suppressRandomness",description:"Do not randomize intervals between samples.",optional:!0,type:"boolean"}]},{name:"stopSampling",description:"Stop collecting native memory profile."},{name:"getAllTimeSamplingProfile",description:"Retrieve native memory allocations profile\ncollected since renderer process startup.",returns:[{name:"profile",$ref:"SamplingProfile"}]},{name:"getBrowserSamplingProfile",description:"Retrieve native memory allocations profile\ncollected since browser process startup.",returns:[{name:"profile",$ref:"SamplingProfile"}]},{name:"getSamplingProfile",description:"Retrieve native memory allocations profile collected since last\n`startSampling` call.",returns:[{name:"profile",$ref:"SamplingProfile"}]}]},{domain:"Network",description:"Network domain allows tracking network activities of the page. It exposes information about http,\nfile, data and other requests and responses, their headers, bodies, timing, etc.",dependencies:["Debugger","Runtime","Security"],types:[{id:"LoaderId",description:"Unique loader identifier.",type:"string"},{id:"RequestId",description:"Unique request identifier.",type:"string"},{id:"InterceptionId",description:"Unique intercepted request identifier.",type:"string"},{id:"ErrorReason",description:"Network level fetch failure reason.",type:"string",enum:["Failed","Aborted","TimedOut","AccessDenied","ConnectionClosed","ConnectionReset","ConnectionRefused","ConnectionAborted","ConnectionFailed","NameNotResolved","InternetDisconnected","AddressUnreachable","BlockedByClient","BlockedByResponse"]},{id:"TimeSinceEpoch",description:"UTC time in seconds, counted from January 1, 1970.",type:"number"},{id:"MonotonicTime",description:"Monotonically increasing time in seconds since an arbitrary point in the past.",type:"number"},{id:"Headers",description:"Request / response headers as keys / values of JSON object.",type:"object"},{id:"ConnectionType",description:"The underlying connection technology that the browser is supposedly using.",type:"string",enum:["none","cellular2g","cellular3g","cellular4g","bluetooth","ethernet","wifi","wimax","other"]},{id:"CookieSameSite",description:"Represents the cookie's 'SameSite' status:\nhttps://tools.ietf.org/html/draft-west-first-party-cookies",type:"string",enum:["Strict","Lax"]},{id:"ResourceTiming",description:"Timing information for the request.",type:"object",properties:[{name:"requestTime",description:"Timing's requestTime is a baseline in seconds, while the other numbers are ticks in\nmilliseconds relatively to this requestTime.",type:"number"},{name:"proxyStart",description:"Started resolving proxy.",type:"number"},{name:"proxyEnd",description:"Finished resolving proxy.",type:"number"},{name:"dnsStart",description:"Started DNS address resolve.",type:"number"},{name:"dnsEnd",description:"Finished DNS address resolve.",type:"number"},{name:"connectStart",description:"Started connecting to the remote host.",type:"number"},{name:"connectEnd",description:"Connected to the remote host.",type:"number"},{name:"sslStart",description:"Started SSL handshake.",type:"number"},{name:"sslEnd",description:"Finished SSL handshake.",type:"number"},{name:"workerStart",description:"Started running ServiceWorker.",experimental:!0,type:"number"},{name:"workerReady",description:"Finished Starting ServiceWorker.",experimental:!0,type:"number"},{name:"sendStart",description:"Started sending request.",type:"number"},{name:"sendEnd",description:"Finished sending request.",type:"number"},{name:"pushStart",description:"Time the server started pushing request.",experimental:!0,type:"number"},{name:"pushEnd",description:"Time the server finished pushing request.",experimental:!0,type:"number"},{name:"receiveHeadersEnd",description:"Finished receiving response headers.",type:"number"}]},{id:"ResourcePriority",description:"Loading priority of a resource request.",type:"string",enum:["VeryLow","Low","Medium","High","VeryHigh"]},{id:"Request",description:"HTTP request data.",type:"object",properties:[{name:"url",description:"Request URL (without fragment).",type:"string"},{name:"urlFragment",description:"Fragment of the requested URL starting with hash, if present.",optional:!0,type:"string"},{name:"method",description:"HTTP request method.",type:"string"},{name:"headers",description:"HTTP request headers.",$ref:"Headers"},{name:"postData",description:"HTTP POST request data.",optional:!0,type:"string"},{name:"hasPostData",description:"True when the request has POST data. Note that postData might still be omitted when this flag is true when the data is too long.",optional:!0,type:"boolean"},{name:"mixedContentType",description:"The mixed content type of the request.",optional:!0,$ref:"Security.MixedContentType"},{name:"initialPriority",description:"Priority of the resource request at the time request is sent.",$ref:"ResourcePriority"},{name:"referrerPolicy",description:"The referrer policy of the request, as defined in https://www.w3.org/TR/referrer-policy/",type:"string",enum:["unsafe-url","no-referrer-when-downgrade","no-referrer","origin","origin-when-cross-origin","same-origin","strict-origin","strict-origin-when-cross-origin"]},{name:"isLinkPreload",description:"Whether is loaded via link preload.",optional:!0,type:"boolean"}]},{id:"SignedCertificateTimestamp",description:"Details of a signed certificate timestamp (SCT).",type:"object",properties:[{name:"status",description:"Validation status.",type:"string"},{name:"origin",description:"Origin.",type:"string"},{name:"logDescription",description:"Log name / description.",type:"string"},{name:"logId",description:"Log ID.",type:"string"},{name:"timestamp",description:"Issuance date.",$ref:"TimeSinceEpoch"},{name:"hashAlgorithm",description:"Hash algorithm.",type:"string"},{name:"signatureAlgorithm",description:"Signature algorithm.",type:"string"},{name:"signatureData",description:"Signature data.",type:"string"}]},{id:"SecurityDetails",description:"Security details about a request.",type:"object",properties:[{name:"protocol",description:'Protocol name (e.g. "TLS 1.2" or "QUIC").',type:"string"},{name:"keyExchange",description:"Key Exchange used by the connection, or the empty string if not applicable.",type:"string"},{name:"keyExchangeGroup",description:"(EC)DH group used by the connection, if applicable.",optional:!0,type:"string"},{name:"cipher",description:"Cipher name.",type:"string"},{name:"mac",description:"TLS MAC. Note that AEAD ciphers do not have separate MACs.",optional:!0,type:"string"},{name:"certificateId",description:"Certificate ID value.",$ref:"Security.CertificateId"},{name:"subjectName",description:"Certificate subject name.",type:"string"},{name:"sanList",description:"Subject Alternative Name (SAN) DNS names and IP addresses.",type:"array",items:{type:"string"}},{name:"issuer",description:"Name of the issuing CA.",type:"string"},{name:"validFrom",description:"Certificate valid from date.",$ref:"TimeSinceEpoch"},{name:"validTo",description:"Certificate valid to (expiration) date",$ref:"TimeSinceEpoch"},{name:"signedCertificateTimestampList",description:"List of signed certificate timestamps (SCTs).",type:"array",items:{$ref:"SignedCertificateTimestamp"}},{name:"certificateTransparencyCompliance",description:"Whether the request complied with Certificate Transparency policy",$ref:"CertificateTransparencyCompliance"}]},{id:"CertificateTransparencyCompliance",description:"Whether the request complied with Certificate Transparency policy.",type:"string",enum:["unknown","not-compliant","compliant"]},{id:"BlockedReason",description:"The reason why request was blocked.",type:"string",enum:["other","csp","mixed-content","origin","inspector","subresource-filter","content-type","collapsed-by-client"]},{id:"Response",description:"HTTP response data.",type:"object",properties:[{name:"url",description:"Response URL. This URL can be different from CachedResource.url in case of redirect.",type:"string"},{name:"status",description:"HTTP response status code.",type:"integer"},{name:"statusText",description:"HTTP response status text.",type:"string"},{name:"headers",description:"HTTP response headers.",$ref:"Headers"},{name:"headersText",description:"HTTP response headers text.",optional:!0,type:"string"},{name:"mimeType",description:"Resource mimeType as determined by the browser.",type:"string"},{name:"requestHeaders",description:"Refined HTTP request headers that were actually transmitted over the network.",optional:!0,$ref:"Headers"},{name:"requestHeadersText",description:"HTTP request headers text.",optional:!0,type:"string"},{name:"connectionReused",description:"Specifies whether physical connection was actually reused for this request.",type:"boolean"},{name:"connectionId",description:"Physical connection id that was actually used for this request.",type:"number"},{name:"remoteIPAddress",description:"Remote IP address.",optional:!0,type:"string"},{name:"remotePort",description:"Remote port.",optional:!0,type:"integer"},{name:"fromDiskCache",description:"Specifies that the request was served from the disk cache.",optional:!0,type:"boolean"},{name:"fromServiceWorker",description:"Specifies that the request was served from the ServiceWorker.",optional:!0,type:"boolean"},{name:"encodedDataLength",description:"Total number of bytes received for this request so far.",type:"number"},{name:"timing",description:"Timing information for the given request.",optional:!0,$ref:"ResourceTiming"},{name:"protocol",description:"Protocol used to fetch this request.",optional:!0,type:"string"},{name:"securityState",description:"Security state of the request resource.",$ref:"Security.SecurityState"},{name:"securityDetails",description:"Security details for the request.",optional:!0,$ref:"SecurityDetails"}]},{id:"WebSocketRequest",description:"WebSocket request data.",type:"object",properties:[{name:"headers",description:"HTTP request headers.",$ref:"Headers"}]},{id:"WebSocketResponse",description:"WebSocket response data.",type:"object",properties:[{name:"status",description:"HTTP response status code.",type:"integer"},{name:"statusText",description:"HTTP response status text.",type:"string"},{name:"headers",description:"HTTP response headers.",$ref:"Headers"},{name:"headersText",description:"HTTP response headers text.",optional:!0,type:"string"},{name:"requestHeaders",description:"HTTP request headers.",optional:!0,$ref:"Headers"},{name:"requestHeadersText",description:"HTTP request headers text.",optional:!0,type:"string"}]},{id:"WebSocketFrame",description:"WebSocket frame data.",type:"object",properties:[{name:"opcode",description:"WebSocket frame opcode.",type:"number"},{name:"mask",description:"WebSocke frame mask.",type:"boolean"},{name:"payloadData",description:"WebSocke frame payload data.",type:"string"}]},{id:"CachedResource",description:"Information about the cached resource.",type:"object",properties:[{name:"url",description:"Resource URL. This is the url of the original network request.",
type:"string"},{name:"type",description:"Type of this resource.",$ref:"Page.ResourceType"},{name:"response",description:"Cached response data.",optional:!0,$ref:"Response"},{name:"bodySize",description:"Cached response body size.",type:"number"}]},{id:"Initiator",description:"Information about the request initiator.",type:"object",properties:[{name:"type",description:"Type of this initiator.",type:"string",enum:["parser","script","preload","SignedExchange","other"]},{name:"stack",description:"Initiator JavaScript stack trace, set for Script only.",optional:!0,$ref:"Runtime.StackTrace"},{name:"url",description:"Initiator URL, set for Parser type or for Script type (when script is importing module) or for SignedExchange type.",optional:!0,type:"string"},{name:"lineNumber",description:"Initiator line number, set for Parser type or for Script type (when script is importing\nmodule) (0-based).",optional:!0,type:"number"}]},{id:"Cookie",description:"Cookie object",type:"object",properties:[{name:"name",description:"Cookie name.",type:"string"},{name:"value",description:"Cookie value.",type:"string"},{name:"domain",description:"Cookie domain.",type:"string"},{name:"path",description:"Cookie path.",type:"string"},{name:"expires",description:"Cookie expiration date as the number of seconds since the UNIX epoch.",type:"number"},{name:"size",description:"Cookie size.",type:"integer"},{name:"httpOnly",description:"True if cookie is http-only.",type:"boolean"},{name:"secure",description:"True if cookie is secure.",type:"boolean"},{name:"session",description:"True in case of session cookie.",type:"boolean"},{name:"sameSite",description:"Cookie SameSite type.",optional:!0,$ref:"CookieSameSite"}]},{id:"CookieParam",description:"Cookie parameter object",type:"object",properties:[{name:"name",description:"Cookie name.",type:"string"},{name:"value",description:"Cookie value.",type:"string"},{name:"url",description:"The request-URI to associate with the setting of the cookie. This value can affect the\ndefault domain and path values of the created cookie.",optional:!0,type:"string"},{name:"domain",description:"Cookie domain.",optional:!0,type:"string"},{name:"path",description:"Cookie path.",optional:!0,type:"string"},{name:"secure",description:"True if cookie is secure.",optional:!0,type:"boolean"},{name:"httpOnly",description:"True if cookie is http-only.",optional:!0,type:"boolean"},{name:"sameSite",description:"Cookie SameSite type.",optional:!0,$ref:"CookieSameSite"},{name:"expires",description:"Cookie expiration date, session cookie if not set",optional:!0,$ref:"TimeSinceEpoch"}]},{id:"AuthChallenge",description:"Authorization challenge for HTTP status code 401 or 407.",experimental:!0,type:"object",properties:[{name:"source",description:"Source of the authentication challenge.",optional:!0,type:"string",enum:["Server","Proxy"]},{name:"origin",description:"Origin of the challenger.",type:"string"},{name:"scheme",description:"The authentication scheme used, such as basic or digest",type:"string"},{name:"realm",description:"The realm of the challenge. May be empty.",type:"string"}]},{id:"AuthChallengeResponse",description:"Response to an AuthChallenge.",experimental:!0,type:"object",properties:[{name:"response",description:"The decision on what to do in response to the authorization challenge.  Default means\ndeferring to the default behavior of the net stack, which will likely either the Cancel\nauthentication or display a popup dialog box.",type:"string",enum:["Default","CancelAuth","ProvideCredentials"]},{name:"username",description:"The username to provide, possibly empty. Should only be set if response is\nProvideCredentials.",optional:!0,type:"string"},{name:"password",description:"The password to provide, possibly empty. Should only be set if response is\nProvideCredentials.",optional:!0,type:"string"}]},{id:"InterceptionStage",description:"Stages of the interception to begin intercepting. Request will intercept before the request is\nsent. Response will intercept after the response is received.",experimental:!0,type:"string",enum:["Request","HeadersReceived"]},{id:"RequestPattern",description:"Request pattern for interception.",experimental:!0,type:"object",properties:[{name:"urlPattern",description:"Wildcards ('*' -> zero or more, '?' -> exactly one) are allowed. Escape character is\nbackslash. Omitting is equivalent to \"*\".",optional:!0,type:"string"},{name:"resourceType",description:"If set, only requests for matching resource types will be intercepted.",optional:!0,$ref:"Page.ResourceType"},{name:"interceptionStage",description:"Stage at wich to begin intercepting requests. Default is Request.",optional:!0,$ref:"InterceptionStage"}]},{id:"SignedExchangeSignature",description:"Information about a signed exchange signature.\nhttps://wicg.github.io/webpackage/draft-yasskin-httpbis-origin-signed-exchanges-impl.html#rfc.section.3.1",experimental:!0,type:"object",properties:[{name:"label",description:"Signed exchange signature label.",type:"string"},{name:"signature",description:"The hex string of signed exchange signature.",type:"string"},{name:"integrity",description:"Signed exchange signature integrity.",type:"string"},{name:"certUrl",description:"Signed exchange signature cert Url.",optional:!0,type:"string"},{name:"certSha256",description:"The hex string of signed exchange signature cert sha256.",optional:!0,type:"string"},{name:"validityUrl",description:"Signed exchange signature validity Url.",type:"string"},{name:"date",description:"Signed exchange signature date.",type:"integer"},{name:"expires",description:"Signed exchange signature expires.",type:"integer"},{name:"certificates",description:"The encoded certificates.",optional:!0,type:"array",items:{type:"string"}}]},{id:"SignedExchangeHeader",description:"Information about a signed exchange header.\nhttps://wicg.github.io/webpackage/draft-yasskin-httpbis-origin-signed-exchanges-impl.html#cbor-representation",experimental:!0,type:"object",properties:[{name:"requestUrl",description:"Signed exchange request URL.",type:"string"},{name:"requestMethod",description:"Signed exchange request method.",type:"string"},{name:"responseCode",description:"Signed exchange response code.",type:"integer"},{name:"responseHeaders",description:"Signed exchange response headers.",$ref:"Headers"},{name:"signatures",description:"Signed exchange response signature.",type:"array",items:{$ref:"SignedExchangeSignature"}}]},{id:"SignedExchangeErrorField",description:"Field type for a signed exchange related error.",experimental:!0,type:"string",enum:["signatureSig","signatureIntegrity","signatureCertUrl","signatureCertSha256","signatureValidityUrl","signatureTimestamps"]},{id:"SignedExchangeError",description:"Information about a signed exchange response.",experimental:!0,type:"object",properties:[{name:"message",description:"Error message.",type:"string"},{name:"signatureIndex",description:"The index of the signature which caused the error.",optional:!0,type:"integer"},{name:"errorField",description:"The field which caused the error.",optional:!0,$ref:"SignedExchangeErrorField"}]},{id:"SignedExchangeInfo",description:"Information about a signed exchange response.",experimental:!0,type:"object",properties:[{name:"outerResponse",description:"The outer response of signed HTTP exchange which was received from network.",$ref:"Response"},{name:"header",description:"Information about the signed exchange header.",optional:!0,$ref:"SignedExchangeHeader"},{name:"securityDetails",description:"Security details for the signed exchange header.",optional:!0,$ref:"SecurityDetails"},{name:"errors",description:"Errors occurred while handling the signed exchagne.",optional:!0,type:"array",items:{$ref:"SignedExchangeError"}}]}],commands:[{name:"canClearBrowserCache",description:"Tells whether clearing browser cache is supported.",deprecated:!0,returns:[{name:"result",description:"True if browser cache can be cleared.",type:"boolean"}]},{name:"canClearBrowserCookies",description:"Tells whether clearing browser cookies is supported.",deprecated:!0,returns:[{name:"result",description:"True if browser cookies can be cleared.",type:"boolean"}]},{name:"canEmulateNetworkConditions",description:"Tells whether emulation of network conditions is supported.",deprecated:!0,returns:[{name:"result",description:"True if emulation of network conditions is supported.",type:"boolean"}]},{name:"clearBrowserCache",description:"Clears browser cache."},{name:"clearBrowserCookies",description:"Clears browser cookies."},{name:"continueInterceptedRequest",description:"Response to Network.requestIntercepted which either modifies the request to continue with any\nmodifications, or blocks it, or completes it with the provided response bytes. If a network\nfetch occurs as a result which encounters a redirect an additional Network.requestIntercepted\nevent will be sent with the same InterceptionId.",experimental:!0,parameters:[{name:"interceptionId",$ref:"InterceptionId"},{name:"errorReason",description:"If set this causes the request to fail with the given reason. Passing `Aborted` for requests\nmarked with `isNavigationRequest` also cancels the navigation. Must not be set in response\nto an authChallenge.",optional:!0,$ref:"ErrorReason"},{name:"rawResponse",description:"If set the requests completes using with the provided base64 encoded raw response, including\nHTTP status line and headers etc... Must not be set in response to an authChallenge.",optional:!0,type:"string"},{name:"url",description:"If set the request url will be modified in a way that's not observable by page. Must not be\nset in response to an authChallenge.",optional:!0,type:"string"},{name:"method",description:"If set this allows the request method to be overridden. Must not be set in response to an\nauthChallenge.",optional:!0,type:"string"},{name:"postData",description:"If set this allows postData to be set. Must not be set in response to an authChallenge.",optional:!0,type:"string"},{name:"headers",description:"If set this allows the request headers to be changed. Must not be set in response to an\nauthChallenge.",optional:!0,$ref:"Headers"},{name:"authChallengeResponse",description:"Response to a requestIntercepted with an authChallenge. Must not be set otherwise.",optional:!0,$ref:"AuthChallengeResponse"}]},{name:"deleteCookies",description:"Deletes browser cookies with matching name and url or domain/path pair.",parameters:[{name:"name",description:"Name of the cookies to remove.",type:"string"},{name:"url",description:"If specified, deletes all the cookies with the given name where domain and path match\nprovided URL.",optional:!0,type:"string"},{name:"domain",description:"If specified, deletes only cookies with the exact domain.",optional:!0,type:"string"},{name:"path",description:"If specified, deletes only cookies with the exact path.",optional:!0,type:"string"}]},{name:"disable",description:"Disables network tracking, prevents network events from being sent to the client."},{name:"emulateNetworkConditions",description:"Activates emulation of network conditions.",parameters:[{name:"offline",description:"True to emulate internet disconnection.",type:"boolean"},{name:"latency",description:"Minimum latency from request sent to response headers received (ms).",type:"number"},{name:"downloadThroughput",description:"Maximal aggregated download throughput (bytes/sec). -1 disables download throttling.",type:"number"},{name:"uploadThroughput",description:"Maximal aggregated upload throughput (bytes/sec).  -1 disables upload throttling.",type:"number"},{name:"connectionType",description:"Connection type if known.",optional:!0,$ref:"ConnectionType"}]},{name:"enable",description:"Enables network tracking, network events will now be delivered to the client.",parameters:[{name:"maxTotalBufferSize",description:"Buffer size in bytes to use when preserving network payloads (XHRs, etc).",experimental:!0,optional:!0,type:"integer"},{name:"maxResourceBufferSize",description:"Per-resource buffer size in bytes to use when preserving network payloads (XHRs, etc).",experimental:!0,optional:!0,type:"integer"},{name:"maxPostDataSize",description:"Longest post body size (in bytes) that would be included in requestWillBeSent notification",optional:!0,type:"integer"}]},{name:"getAllCookies",description:"Returns all browser cookies. Depending on the backend support, will return detailed cookie\ninformation in the `cookies` field.",returns:[{name:"cookies",description:"Array of cookie objects.",type:"array",items:{$ref:"Cookie"}}]},{name:"getCertificate",description:"Returns the DER-encoded certificate.",experimental:!0,parameters:[{name:"origin",description:"Origin to get certificate for.",type:"string"}],returns:[{name:"tableNames",type:"array",items:{type:"string"}}]},{name:"getCookies",description:"Returns all browser cookies for the current URL. Depending on the backend support, will return\ndetailed cookie information in the `cookies` field.",parameters:[{name:"urls",description:"The list of URLs for which applicable cookies will be fetched",optional:!0,type:"array",items:{type:"string"}}],returns:[{name:"cookies",description:"Array of cookie objects.",type:"array",items:{$ref:"Cookie"}}]},{name:"getResponseBody",description:"Returns content served for the given request.",parameters:[{name:"requestId",description:"Identifier of the network request to get content for.",$ref:"RequestId"}],returns:[{name:"body",description:"Response body.",type:"string"},{name:"base64Encoded",description:"True, if content was sent as base64.",type:"boolean"}]},{name:"getRequestPostData",description:"Returns post data sent with the request. Returns an error when no data was sent with the request.",parameters:[{name:"requestId",description:"Identifier of the network request to get content for.",$ref:"RequestId"}],returns:[{name:"postData",description:"Base64-encoded request body.",type:"string"}]},{name:"getResponseBodyForInterception",description:"Returns content served for the given currently intercepted request.",experimental:!0,parameters:[{name:"interceptionId",description:"Identifier for the intercepted request to get body for.",$ref:"InterceptionId"}],returns:[{name:"body",description:"Response body.",type:"string"},{name:"base64Encoded",description:"True, if content was sent as base64.",type:"boolean"}]},{name:"takeResponseBodyForInterceptionAsStream",description:"Returns a handle to the stream representing the response body. Note that after this command,\nthe intercepted request can't be continued as is -- you either need to cancel it or to provide\nthe response body. The stream only supports sequential read, IO.read will fail if the position\nis specified.",experimental:!0,parameters:[{name:"interceptionId",$ref:"InterceptionId"}],returns:[{name:"stream",$ref:"IO.StreamHandle"}]},{name:"replayXHR",description:"This method sends a new XMLHttpRequest which is identical to the original one. The following\nparameters should be identical: method, url, async, request body, extra headers, withCredentials\nattribute, user, password.",experimental:!0,parameters:[{name:"requestId",description:"Identifier of XHR to replay.",$ref:"RequestId"}]},{name:"searchInResponseBody",description:"Searches for given string in response content.",experimental:!0,parameters:[{name:"requestId",description:"Identifier of the network response to search.",$ref:"RequestId"},{name:"query",description:"String to search for.",type:"string"},{name:"caseSensitive",description:"If true, search is case sensitive.",optional:!0,type:"boolean"},{name:"isRegex",description:"If true, treats string parameter as regex.",optional:!0,type:"boolean"}],returns:[{name:"result",description:"List of search matches.",type:"array",items:{$ref:"Debugger.SearchMatch"}}]},{name:"setBlockedURLs",description:"Blocks URLs from loading.",experimental:!0,parameters:[{name:"urls",description:"URL patterns to block. Wildcards ('*') are allowed.",type:"array",items:{type:"string"}}]},{name:"setBypassServiceWorker",description:"Toggles ignoring of service worker for each request.",experimental:!0,parameters:[{name:"bypass",description:"Bypass service worker and load from network.",type:"boolean"}]},{name:"setCacheDisabled",description:"Toggles ignoring cache for each request. If `true`, cache will not be used.",parameters:[{name:"cacheDisabled",description:"Cache disabled state.",type:"boolean"}]},{name:"setCookie",description:"Sets a cookie with the given cookie data; may overwrite equivalent cookies if they exist.",parameters:[{name:"name",description:"Cookie name.",type:"string"},{name:"value",description:"Cookie value.",type:"string"},{name:"url",description:"The request-URI to associate with the setting of the cookie. This value can affect the\ndefault domain and path values of the created cookie.",optional:!0,type:"string"},{name:"domain",description:"Cookie domain.",optional:!0,type:"string"},{name:"path",description:"Cookie path.",optional:!0,type:"string"},{name:"secure",description:"True if cookie is secure.",optional:!0,type:"boolean"},{name:"httpOnly",description:"True if cookie is http-only.",optional:!0,type:"boolean"},{name:"sameSite",description:"Cookie SameSite type.",optional:!0,$ref:"CookieSameSite"},{name:"expires",description:"Cookie expiration date, session cookie if not set",optional:!0,$ref:"TimeSinceEpoch"}],returns:[{name:"success",description:"True if successfully set cookie.",type:"boolean"}]},{name:"setCookies",description:"Sets given cookies.",parameters:[{name:"cookies",description:"Cookies to be set.",type:"array",items:{$ref:"CookieParam"}}]},{name:"setDataSizeLimitsForTest",description:"For testing.",experimental:!0,parameters:[{name:"maxTotalSize",description:"Maximum total buffer size.",type:"integer"},{name:"maxResourceSize",description:"Maximum per-resource size.",type:"integer"}]},{name:"setExtraHTTPHeaders",description:"Specifies whether to always send extra HTTP headers with the requests from this page.",parameters:[{name:"headers",description:"Map with extra HTTP headers.",$ref:"Headers"}]},{name:"setRequestInterception",description:"Sets the requests to intercept that match a the provided patterns and optionally resource types.",experimental:!0,parameters:[{name:"patterns",description:"Requests matching any of these patterns will be forwarded and wait for the corresponding\ncontinueInterceptedRequest call.",type:"array",items:{$ref:"RequestPattern"}}]},{name:"setUserAgentOverride",description:"Allows overriding user agent with the given string.",redirect:"Emulation",parameters:[{name:"userAgent",description:"User agent to use.",type:"string"},{name:"acceptLanguage",description:"Browser langugage to emulate.",optional:!0,type:"string"},{name:"platform",description:"The platform navigator.platform should return.",optional:!0,type:"string"}]}],events:[{name:"dataReceived",description:"Fired when data chunk was received over the network.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"},{name:"dataLength",description:"Data chunk length.",type:"integer"},{name:"encodedDataLength",description:"Actual bytes received (might be less than dataLength for compressed encodings).",type:"integer"}]},{name:"eventSourceMessageReceived",description:"Fired when EventSource message is received.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"},{name:"eventName",description:"Message type.",type:"string"},{name:"eventId",description:"Message identifier.",type:"string"},{name:"data",description:"Message content.",type:"string"}]},{name:"loadingFailed",description:"Fired when HTTP request has failed to load.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"},{name:"type",description:"Resource type.",$ref:"Page.ResourceType"},{name:"errorText",description:"User friendly error message.",type:"string"},{name:"canceled",description:"True if loading was canceled.",optional:!0,type:"boolean"},{name:"blockedReason",description:"The reason why loading was blocked, if any.",optional:!0,$ref:"BlockedReason"}]},{name:"loadingFinished",description:"Fired when HTTP request has finished loading.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"},{name:"encodedDataLength",description:"Total number of bytes received for this request.",type:"number"},{name:"shouldReportCorbBlocking",description:"Set when 1) response was blocked by Cross-Origin Read Blocking and also\n2) this needs to be reported to the DevTools console.",optional:!0,type:"boolean"}]},{name:"requestIntercepted",description:"Details of an intercepted HTTP request, which must be either allowed, blocked, modified or\nmocked.",experimental:!0,parameters:[{name:"interceptionId",description:"Each request the page makes will have a unique id, however if any redirects are encountered\nwhile processing that fetch, they will be reported with the same id as the original fetch.\nLikewise if HTTP authentication is needed then the same fetch id will be used.",$ref:"InterceptionId"},{name:"request",$ref:"Request"},{name:"frameId",description:"The id of the frame that initiated the request.",$ref:"Page.FrameId"},{name:"resourceType",description:"How the requested resource will be used.",$ref:"Page.ResourceType"},{name:"isNavigationRequest",description:"Whether this is a navigation request, which can abort the navigation completely.",type:"boolean"},{name:"isDownload",description:"Set if the request is a navigation that will result in a download.\nOnly present after response is received from the server (i.e. HeadersReceived stage).",optional:!0,type:"boolean"},{name:"redirectUrl",description:"Redirect location, only sent if a redirect was intercepted.",optional:!0,type:"string"},{name:"authChallenge",description:"Details of the Authorization Challenge encountered. If this is set then\ncontinueInterceptedRequest must contain an authChallengeResponse.",optional:!0,$ref:"AuthChallenge"},{name:"responseErrorReason",description:"Response error if intercepted at response stage or if redirect occurred while intercepting\nrequest.",optional:!0,$ref:"ErrorReason"},{name:"responseStatusCode",description:"Response code if intercepted at response stage or if redirect occurred while intercepting\nrequest or auth retry occurred.",optional:!0,type:"integer"},{name:"responseHeaders",description:"Response headers if intercepted at the response stage or if redirect occurred while\nintercepting request or auth retry occurred.",optional:!0,$ref:"Headers"}]},{name:"requestServedFromCache",description:"Fired if request ended up loading from cache.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"}]},{name:"requestWillBeSent",description:"Fired when page is about to send HTTP request.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"loaderId",description:"Loader identifier. Empty string if the request is fetched from worker.",$ref:"LoaderId"},{name:"documentURL",description:"URL of the document this request is loaded for.",type:"string"},{name:"request",description:"Request data.",$ref:"Request"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"},{name:"wallTime",description:"Timestamp.",$ref:"TimeSinceEpoch"},{name:"initiator",description:"Request initiator.",$ref:"Initiator"},{name:"redirectResponse",description:"Redirect response data.",optional:!0,$ref:"Response"},{name:"type",description:"Type of this resource.",optional:!0,$ref:"Page.ResourceType"},{name:"frameId",description:"Frame identifier.",optional:!0,$ref:"Page.FrameId"},{name:"hasUserGesture",description:"Whether the request is initiated by a user gesture. Defaults to false.",optional:!0,type:"boolean"}]},{name:"resourceChangedPriority",description:"Fired when resource loading priority is changed",experimental:!0,parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"newPriority",description:"New priority",$ref:"ResourcePriority"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"}]},{name:"signedExchangeReceived",description:"Fired when a signed exchange was received over the network",experimental:!0,parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"info",description:"Information about the signed exchange response.",$ref:"SignedExchangeInfo"}]},{name:"responseReceived",description:"Fired when HTTP response is available.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"loaderId",description:"Loader identifier. Empty string if the request is fetched from worker.",$ref:"LoaderId"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"},{name:"type",description:"Resource type.",$ref:"Page.ResourceType"},{name:"response",description:"Response data.",$ref:"Response"},{name:"frameId",description:"Frame identifier.",optional:!0,$ref:"Page.FrameId"}]},{name:"webSocketClosed",description:"Fired when WebSocket is closed.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"}]},{name:"webSocketCreated",description:"Fired upon WebSocket creation.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"url",description:"WebSocket request URL.",type:"string"},{name:"initiator",description:"Request initiator.",optional:!0,$ref:"Initiator"}]},{name:"webSocketFrameError",description:"Fired when WebSocket frame error occurs.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"},{name:"errorMessage",description:"WebSocket frame error message.",type:"string"}]},{name:"webSocketFrameReceived",description:"Fired when WebSocket frame is received.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"},{name:"response",description:"WebSocket response data.",$ref:"WebSocketFrame"}]},{name:"webSocketFrameSent",description:"Fired when WebSocket frame is sent.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"},{name:"response",description:"WebSocket response data.",$ref:"WebSocketFrame"}]},{name:"webSocketHandshakeResponseReceived",description:"Fired when WebSocket handshake response becomes available.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"},{name:"response",description:"WebSocket response data.",$ref:"WebSocketResponse"}]},{name:"webSocketWillSendHandshakeRequest",description:"Fired when WebSocket is about to initiate handshake.",parameters:[{name:"requestId",description:"Request identifier.",$ref:"RequestId"},{name:"timestamp",description:"Timestamp.",$ref:"MonotonicTime"},{name:"wallTime",description:"UTC Timestamp.",$ref:"TimeSinceEpoch"},{name:"request",description:"WebSocket request data.",$ref:"WebSocketRequest"}]}]},{domain:"Overlay",description:"This domain provides various functionality related to drawing atop the inspected page.",experimental:!0,dependencies:["DOM","Page","Runtime"],types:[{id:"HighlightConfig",description:"Configuration data for the highlighting of page elements.",type:"object",properties:[{name:"showInfo",description:"Whether the node info tooltip should be shown (default: false).",optional:!0,type:"boolean"},{name:"showRulers",description:"Whether the rulers should be shown (default: false).",optional:!0,type:"boolean"},{name:"showExtensionLines",description:"Whether the extension lines from node to the rulers should be shown (default: false).",optional:!0,type:"boolean"},{name:"displayAsMaterial",optional:!0,type:"boolean"},{name:"contentColor",description:"The content box highlight fill color (default: transparent).",optional:!0,$ref:"DOM.RGBA"},{name:"paddingColor",description:"The padding highlight fill color (default: transparent).",optional:!0,$ref:"DOM.RGBA"},{name:"borderColor",description:"The border highlight fill color (default: transparent).",optional:!0,$ref:"DOM.RGBA"},{name:"marginColor",description:"The margin highlight fill color (default: transparent).",optional:!0,$ref:"DOM.RGBA"},{name:"eventTargetColor",description:"The event target element highlight fill color (default: transparent).",optional:!0,$ref:"DOM.RGBA"},{name:"shapeColor",description:"The shape outside fill color (default: transparent).",optional:!0,$ref:"DOM.RGBA"},{name:"shapeMarginColor",description:"The shape margin fill color (default: transparent).",optional:!0,$ref:"DOM.RGBA"},{name:"selectorList",description:"Selectors to highlight relevant nodes.",optional:!0,type:"string"},{name:"cssGridColor",description:"The grid layout color (default: transparent).",optional:!0,$ref:"DOM.RGBA"}]},{id:"InspectMode",type:"string",enum:["searchForNode","searchForUAShadowDOM","none"]}],commands:[{name:"disable",description:"Disables domain notifications."},{name:"enable",description:"Enables domain notifications."},{name:"getHighlightObjectForTest",description:"For testing.",parameters:[{name:"nodeId",description:"Id of the node to get highlight object for.",$ref:"DOM.NodeId"}],returns:[{name:"highlight",description:"Highlight data for the node.",type:"object"}]},{name:"hideHighlight",description:"Hides any highlight."},{name:"highlightFrame",description:"Highlights owner element of the frame with given id.",parameters:[{name:"frameId",description:"Identifier of the frame to highlight.",$ref:"Page.FrameId"},{name:"contentColor",description:"The content box highlight fill color (default: transparent).",optional:!0,$ref:"DOM.RGBA"},{name:"contentOutlineColor",description:"The content box highlight outline color (default: transparent).",optional:!0,$ref:"DOM.RGBA"}]},{name:"highlightNode",description:"Highlights DOM node with given id or with the given JavaScript object wrapper. Either nodeId or\nobjectId must be specified.",parameters:[{name:"highlightConfig",description:"A descriptor for the highlight appearance.",$ref:"HighlightConfig"},{name:"nodeId",description:"Identifier of the node to highlight.",optional:!0,$ref:"DOM.NodeId"},{name:"backendNodeId",description:"Identifier of the backend node to highlight.",optional:!0,$ref:"DOM.BackendNodeId"},{name:"objectId",description:"JavaScript object id of the node to be highlighted.",optional:!0,$ref:"Runtime.RemoteObjectId"}]},{name:"highlightQuad",description:"Highlights given quad. Coordinates are absolute with respect to the main frame viewport.",parameters:[{name:"quad",description:"Quad to highlight",$ref:"DOM.Quad"},{name:"color",description:"The highlight fill color (default: transparent).",optional:!0,$ref:"DOM.RGBA"},{name:"outlineColor",description:"The highlight outline color (default: transparent).",optional:!0,$ref:"DOM.RGBA"}]},{name:"highlightRect",description:"Highlights given rectangle. Coordinates are absolute with respect to the main frame viewport.",parameters:[{name:"x",description:"X coordinate",type:"integer"},{name:"y",description:"Y coordinate",type:"integer"},{name:"width",description:"Rectangle width",type:"integer"},{name:"height",description:"Rectangle height",type:"integer"},{name:"color",description:"The highlight fill color (default: transparent).",optional:!0,$ref:"DOM.RGBA"},{name:"outlineColor",description:"The highlight outline color (default: transparent).",optional:!0,$ref:"DOM.RGBA"}]},{name:"setInspectMode",description:"Enters the 'inspect' mode. In this mode, elements that user is hovering over are highlighted.\nBackend then generates 'inspectNodeRequested' event upon element selection.",
parameters:[{name:"mode",description:"Set an inspection mode.",$ref:"InspectMode"},{name:"highlightConfig",description:"A descriptor for the highlight appearance of hovered-over nodes. May be omitted if `enabled\n== false`.",optional:!0,$ref:"HighlightConfig"}]},{name:"setPausedInDebuggerMessage",parameters:[{name:"message",description:"The message to display, also triggers resume and step over controls.",optional:!0,type:"string"}]},{name:"setShowDebugBorders",description:"Requests that backend shows debug borders on layers",parameters:[{name:"show",description:"True for showing debug borders",type:"boolean"}]},{name:"setShowFPSCounter",description:"Requests that backend shows the FPS counter",parameters:[{name:"show",description:"True for showing the FPS counter",type:"boolean"}]},{name:"setShowPaintRects",description:"Requests that backend shows paint rectangles",parameters:[{name:"result",description:"True for showing paint rectangles",type:"boolean"}]},{name:"setShowScrollBottleneckRects",description:"Requests that backend shows scroll bottleneck rects",parameters:[{name:"show",description:"True for showing scroll bottleneck rects",type:"boolean"}]},{name:"setShowViewportSizeOnResize",description:"Paints viewport size upon main frame resize.",parameters:[{name:"show",description:"Whether to paint size or not.",type:"boolean"}]},{name:"setSuspended",parameters:[{name:"suspended",description:"Whether overlay should be suspended and not consume any resources until resumed.",type:"boolean"}]}],events:[{name:"inspectNodeRequested",description:"Fired when the node should be inspected. This happens after call to `setInspectMode` or when\nuser manually inspects an element.",parameters:[{name:"backendNodeId",description:"Id of the node to inspect.",$ref:"DOM.BackendNodeId"}]},{name:"nodeHighlightRequested",description:"Fired when the node should be highlighted. This happens after call to `setInspectMode`.",parameters:[{name:"nodeId",$ref:"DOM.NodeId"}]},{name:"screenshotRequested",description:"Fired when user asks to capture screenshot of some area on the page.",parameters:[{name:"viewport",description:"Viewport to capture, in CSS.",$ref:"Page.Viewport"}]}]},{domain:"Page",description:"Actions and events related to the inspected page belong to the page domain.",dependencies:["Debugger","DOM","Network"],types:[{id:"ResourceType",description:"Resource type as it was perceived by the rendering engine.",type:"string",enum:["Document","Stylesheet","Image","Media","Font","Script","TextTrack","XHR","Fetch","EventSource","WebSocket","Manifest","SignedExchange","Ping","CSPViolationReport","Other"]},{id:"FrameId",description:"Unique frame identifier.",type:"string"},{id:"Frame",description:"Information about the Frame on the page.",type:"object",properties:[{name:"id",description:"Frame unique identifier.",type:"string"},{name:"parentId",description:"Parent frame identifier.",optional:!0,type:"string"},{name:"loaderId",description:"Identifier of the loader associated with this frame.",$ref:"Network.LoaderId"},{name:"name",description:"Frame's name as specified in the tag.",optional:!0,type:"string"},{name:"url",description:"Frame document's URL.",type:"string"},{name:"securityOrigin",description:"Frame document's security origin.",type:"string"},{name:"mimeType",description:"Frame document's mimeType as determined by the browser.",type:"string"},{name:"unreachableUrl",description:"If the frame failed to load, this contains the URL that could not be loaded.",experimental:!0,optional:!0,type:"string"}]},{id:"FrameResource",description:"Information about the Resource on the page.",experimental:!0,type:"object",properties:[{name:"url",description:"Resource URL.",type:"string"},{name:"type",description:"Type of this resource.",$ref:"ResourceType"},{name:"mimeType",description:"Resource mimeType as determined by the browser.",type:"string"},{name:"lastModified",description:"last-modified timestamp as reported by server.",optional:!0,$ref:"Network.TimeSinceEpoch"},{name:"contentSize",description:"Resource content size.",optional:!0,type:"number"},{name:"failed",description:"True if the resource failed to load.",optional:!0,type:"boolean"},{name:"canceled",description:"True if the resource was canceled during loading.",optional:!0,type:"boolean"}]},{id:"FrameResourceTree",description:"Information about the Frame hierarchy along with their cached resources.",experimental:!0,type:"object",properties:[{name:"frame",description:"Frame information for this tree item.",$ref:"Frame"},{name:"childFrames",description:"Child frames.",optional:!0,type:"array",items:{$ref:"FrameResourceTree"}},{name:"resources",description:"Information about frame resources.",type:"array",items:{$ref:"FrameResource"}}]},{id:"FrameTree",description:"Information about the Frame hierarchy.",type:"object",properties:[{name:"frame",description:"Frame information for this tree item.",$ref:"Frame"},{name:"childFrames",description:"Child frames.",optional:!0,type:"array",items:{$ref:"FrameTree"}}]},{id:"ScriptIdentifier",description:"Unique script identifier.",type:"string"},{id:"TransitionType",description:"Transition type.",type:"string",enum:["link","typed","auto_bookmark","auto_subframe","manual_subframe","generated","auto_toplevel","form_submit","reload","keyword","keyword_generated","other"]},{id:"NavigationEntry",description:"Navigation history entry.",type:"object",properties:[{name:"id",description:"Unique id of the navigation history entry.",type:"integer"},{name:"url",description:"URL of the navigation history entry.",type:"string"},{name:"userTypedURL",description:"URL that the user typed in the url bar.",type:"string"},{name:"title",description:"Title of the navigation history entry.",type:"string"},{name:"transitionType",description:"Transition type.",$ref:"TransitionType"}]},{id:"ScreencastFrameMetadata",description:"Screencast frame metadata.",experimental:!0,type:"object",properties:[{name:"offsetTop",description:"Top offset in DIP.",type:"number"},{name:"pageScaleFactor",description:"Page scale factor.",type:"number"},{name:"deviceWidth",description:"Device screen width in DIP.",type:"number"},{name:"deviceHeight",description:"Device screen height in DIP.",type:"number"},{name:"scrollOffsetX",description:"Position of horizontal scroll in CSS pixels.",type:"number"},{name:"scrollOffsetY",description:"Position of vertical scroll in CSS pixels.",type:"number"},{name:"timestamp",description:"Frame swap timestamp.",optional:!0,$ref:"Network.TimeSinceEpoch"}]},{id:"DialogType",description:"Javascript dialog type.",type:"string",enum:["alert","confirm","prompt","beforeunload"]},{id:"AppManifestError",description:"Error while paring app manifest.",type:"object",properties:[{name:"message",description:"Error message.",type:"string"},{name:"critical",description:"If criticial, this is a non-recoverable parse error.",type:"integer"},{name:"line",description:"Error line.",type:"integer"},{name:"column",description:"Error column.",type:"integer"}]},{id:"LayoutViewport",description:"Layout viewport position and dimensions.",type:"object",properties:[{name:"pageX",description:"Horizontal offset relative to the document (CSS pixels).",type:"integer"},{name:"pageY",description:"Vertical offset relative to the document (CSS pixels).",type:"integer"},{name:"clientWidth",description:"Width (CSS pixels), excludes scrollbar if present.",type:"integer"},{name:"clientHeight",description:"Height (CSS pixels), excludes scrollbar if present.",type:"integer"}]},{id:"VisualViewport",description:"Visual viewport position, dimensions, and scale.",type:"object",properties:[{name:"offsetX",description:"Horizontal offset relative to the layout viewport (CSS pixels).",type:"number"},{name:"offsetY",description:"Vertical offset relative to the layout viewport (CSS pixels).",type:"number"},{name:"pageX",description:"Horizontal offset relative to the document (CSS pixels).",type:"number"},{name:"pageY",description:"Vertical offset relative to the document (CSS pixels).",type:"number"},{name:"clientWidth",description:"Width (CSS pixels), excludes scrollbar if present.",type:"number"},{name:"clientHeight",description:"Height (CSS pixels), excludes scrollbar if present.",type:"number"},{name:"scale",description:"Scale relative to the ideal viewport (size at width=device-width).",type:"number"}]},{id:"Viewport",description:"Viewport for capturing screenshot.",type:"object",properties:[{name:"x",description:"X offset in CSS pixels.",type:"number"},{name:"y",description:"Y offset in CSS pixels",type:"number"},{name:"width",description:"Rectangle width in CSS pixels",type:"number"},{name:"height",description:"Rectangle height in CSS pixels",type:"number"},{name:"scale",description:"Page scale factor.",type:"number"}]},{id:"FontFamilies",description:"Generic font families collection.",experimental:!0,type:"object",properties:[{name:"standard",description:"The standard font-family.",optional:!0,type:"string"},{name:"fixed",description:"The fixed font-family.",optional:!0,type:"string"},{name:"serif",description:"The serif font-family.",optional:!0,type:"string"},{name:"sansSerif",description:"The sansSerif font-family.",optional:!0,type:"string"},{name:"cursive",description:"The cursive font-family.",optional:!0,type:"string"},{name:"fantasy",description:"The fantasy font-family.",optional:!0,type:"string"},{name:"pictograph",description:"The pictograph font-family.",optional:!0,type:"string"}]},{id:"FontSizes",description:"Default font sizes.",experimental:!0,type:"object",properties:[{name:"standard",description:"Default standard font size.",optional:!0,type:"integer"},{name:"fixed",description:"Default fixed font size.",optional:!0,type:"integer"}]}],commands:[{name:"addScriptToEvaluateOnLoad",description:"Deprecated, please use addScriptToEvaluateOnNewDocument instead.",experimental:!0,deprecated:!0,parameters:[{name:"scriptSource",type:"string"}],returns:[{name:"identifier",description:"Identifier of the added script.",$ref:"ScriptIdentifier"}]},{name:"addScriptToEvaluateOnNewDocument",description:"Evaluates given script in every frame upon creation (before loading frame's scripts).",parameters:[{name:"source",type:"string"}],returns:[{name:"identifier",description:"Identifier of the added script.",$ref:"ScriptIdentifier"}]},{name:"bringToFront",description:"Brings page to front (activates tab)."},{name:"captureScreenshot",description:"Capture page screenshot.",parameters:[{name:"format",description:"Image compression format (defaults to png).",optional:!0,type:"string",enum:["jpeg","png"]},{name:"quality",description:"Compression quality from range [0..100] (jpeg only).",optional:!0,type:"integer"},{name:"clip",description:"Capture the screenshot of a given region only.",optional:!0,$ref:"Viewport"},{name:"fromSurface",description:"Capture the screenshot from the surface, rather than the view. Defaults to true.",experimental:!0,optional:!0,type:"boolean"}],returns:[{name:"data",description:"Base64-encoded image data.",type:"string"}]},{name:"clearDeviceMetricsOverride",description:"Clears the overriden device metrics.",experimental:!0,deprecated:!0,redirect:"Emulation"},{name:"clearDeviceOrientationOverride",description:"Clears the overridden Device Orientation.",experimental:!0,deprecated:!0,redirect:"DeviceOrientation"},{name:"clearGeolocationOverride",description:"Clears the overriden Geolocation Position and Error.",deprecated:!0,redirect:"Emulation"},{name:"createIsolatedWorld",description:"Creates an isolated world for the given frame.",parameters:[{name:"frameId",description:"Id of the frame in which the isolated world should be created.",$ref:"FrameId"},{name:"worldName",description:"An optional name which is reported in the Execution Context.",optional:!0,type:"string"},{name:"grantUniveralAccess",description:"Whether or not universal access should be granted to the isolated world. This is a powerful\noption, use with caution.",optional:!0,type:"boolean"}],returns:[{name:"executionContextId",description:"Execution context of the isolated world.",$ref:"Runtime.ExecutionContextId"}]},{name:"deleteCookie",description:"Deletes browser cookie with given name, domain and path.",experimental:!0,deprecated:!0,redirect:"Network",parameters:[{name:"cookieName",description:"Name of the cookie to remove.",type:"string"},{name:"url",description:"URL to match cooke domain and path.",type:"string"}]},{name:"disable",description:"Disables page domain notifications."},{name:"enable",description:"Enables page domain notifications."},{name:"getAppManifest",returns:[{name:"url",description:"Manifest location.",type:"string"},{name:"errors",type:"array",items:{$ref:"AppManifestError"}},{name:"data",description:"Manifest content.",optional:!0,type:"string"}]},{name:"getCookies",description:"Returns all browser cookies. Depending on the backend support, will return detailed cookie\ninformation in the `cookies` field.",experimental:!0,deprecated:!0,redirect:"Network",returns:[{name:"cookies",description:"Array of cookie objects.",type:"array",items:{$ref:"Network.Cookie"}}]},{name:"getFrameTree",description:"Returns present frame tree structure.",returns:[{name:"frameTree",description:"Present frame tree structure.",$ref:"FrameTree"}]},{name:"getLayoutMetrics",description:"Returns metrics relating to the layouting of the page, such as viewport bounds/scale.",returns:[{name:"layoutViewport",description:"Metrics relating to the layout viewport.",$ref:"LayoutViewport"},{name:"visualViewport",description:"Metrics relating to the visual viewport.",$ref:"VisualViewport"},{name:"contentSize",description:"Size of scrollable area.",$ref:"DOM.Rect"}]},{name:"getNavigationHistory",description:"Returns navigation history for the current page.",returns:[{name:"currentIndex",description:"Index of the current navigation history entry.",type:"integer"},{name:"entries",description:"Array of navigation history entries.",type:"array",items:{$ref:"NavigationEntry"}}]},{name:"getResourceContent",description:"Returns content of the given resource.",experimental:!0,parameters:[{name:"frameId",description:"Frame id to get resource for.",$ref:"FrameId"},{name:"url",description:"URL of the resource to get content for.",type:"string"}],returns:[{name:"content",description:"Resource content.",type:"string"},{name:"base64Encoded",description:"True, if content was served as base64.",type:"boolean"}]},{name:"getResourceTree",description:"Returns present frame / resource tree structure.",experimental:!0,returns:[{name:"frameTree",description:"Present frame / resource tree structure.",$ref:"FrameResourceTree"}]},{name:"handleJavaScriptDialog",description:"Accepts or dismisses a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload).",parameters:[{name:"accept",description:"Whether to accept or dismiss the dialog.",type:"boolean"},{name:"promptText",description:"The text to enter into the dialog prompt before accepting. Used only if this is a prompt\ndialog.",optional:!0,type:"string"}]},{name:"navigate",description:"Navigates current page to the given URL.",parameters:[{name:"url",description:"URL to navigate the page to.",type:"string"},{name:"referrer",description:"Referrer URL.",optional:!0,type:"string"},{name:"transitionType",description:"Intended transition type.",optional:!0,$ref:"TransitionType"},{name:"frameId",description:"Frame id to navigate, if not specified navigates the top frame.",optional:!0,$ref:"FrameId"}],returns:[{name:"frameId",description:"Frame id that has navigated (or failed to navigate)",$ref:"FrameId"},{name:"loaderId",description:"Loader identifier.",optional:!0,$ref:"Network.LoaderId"},{name:"errorText",description:"User friendly error message, present if and only if navigation has failed.",optional:!0,type:"string"}]},{name:"navigateToHistoryEntry",description:"Navigates current page to the given history entry.",parameters:[{name:"entryId",description:"Unique id of the entry to navigate to.",type:"integer"}]},{name:"printToPDF",description:"Print page as PDF.",parameters:[{name:"landscape",description:"Paper orientation. Defaults to false.",optional:!0,type:"boolean"},{name:"displayHeaderFooter",description:"Display header and footer. Defaults to false.",optional:!0,type:"boolean"},{name:"printBackground",description:"Print background graphics. Defaults to false.",optional:!0,type:"boolean"},{name:"scale",description:"Scale of the webpage rendering. Defaults to 1.",optional:!0,type:"number"},{name:"paperWidth",description:"Paper width in inches. Defaults to 8.5 inches.",optional:!0,type:"number"},{name:"paperHeight",description:"Paper height in inches. Defaults to 11 inches.",optional:!0,type:"number"},{name:"marginTop",description:"Top margin in inches. Defaults to 1cm (~0.4 inches).",optional:!0,type:"number"},{name:"marginBottom",description:"Bottom margin in inches. Defaults to 1cm (~0.4 inches).",optional:!0,type:"number"},{name:"marginLeft",description:"Left margin in inches. Defaults to 1cm (~0.4 inches).",optional:!0,type:"number"},{name:"marginRight",description:"Right margin in inches. Defaults to 1cm (~0.4 inches).",optional:!0,type:"number"},{name:"pageRanges",description:"Paper ranges to print, e.g., '1-5, 8, 11-13'. Defaults to the empty string, which means\nprint all pages.",optional:!0,type:"string"},{name:"ignoreInvalidPageRanges",description:"Whether to silently ignore invalid but successfully parsed page ranges, such as '3-2'.\nDefaults to false.",optional:!0,type:"boolean"},{name:"headerTemplate",description:"HTML template for the print header. Should be valid HTML markup with following\nclasses used to inject printing values into them:\n- `date`: formatted print date\n- `title`: document title\n- `url`: document location\n- `pageNumber`: current page number\n- `totalPages`: total pages in the document\n\nFor example, `<span class=title></span>` would generate span containing the title.",optional:!0,type:"string"},{name:"footerTemplate",description:"HTML template for the print footer. Should use the same format as the `headerTemplate`.",optional:!0,type:"string"},{name:"preferCSSPageSize",description:"Whether or not to prefer page size as defined by css. Defaults to false,\nin which case the content will be scaled to fit the paper size.",optional:!0,type:"boolean"}],returns:[{name:"data",description:"Base64-encoded pdf data.",type:"string"}]},{name:"reload",description:"Reloads given page optionally ignoring the cache.",parameters:[{name:"ignoreCache",description:"If true, browser cache is ignored (as if the user pressed Shift+refresh).",optional:!0,type:"boolean"},{name:"scriptToEvaluateOnLoad",description:"If set, the script will be injected into all frames of the inspected page after reload.\nArgument will be ignored if reloading dataURL origin.",optional:!0,type:"string"}]},{name:"removeScriptToEvaluateOnLoad",description:"Deprecated, please use removeScriptToEvaluateOnNewDocument instead.",experimental:!0,deprecated:!0,parameters:[{name:"identifier",$ref:"ScriptIdentifier"}]},{name:"removeScriptToEvaluateOnNewDocument",description:"Removes given script from the list.",parameters:[{name:"identifier",$ref:"ScriptIdentifier"}]},{name:"requestAppBanner",experimental:!0},{name:"screencastFrameAck",description:"Acknowledges that a screencast frame has been received by the frontend.",experimental:!0,parameters:[{name:"sessionId",description:"Frame number.",type:"integer"}]},{name:"searchInResource",description:"Searches for given string in resource content.",experimental:!0,parameters:[{name:"frameId",description:"Frame id for resource to search in.",$ref:"FrameId"},{name:"url",description:"URL of the resource to search in.",type:"string"},{name:"query",description:"String to search for.",type:"string"},{name:"caseSensitive",description:"If true, search is case sensitive.",optional:!0,type:"boolean"},{name:"isRegex",description:"If true, treats string parameter as regex.",optional:!0,type:"boolean"}],returns:[{name:"result",description:"List of search matches.",type:"array",items:{$ref:"Debugger.SearchMatch"}}]},{name:"setAdBlockingEnabled",description:"Enable Chrome's experimental ad filter on all sites.",experimental:!0,parameters:[{name:"enabled",description:"Whether to block ads.",type:"boolean"}]},{name:"setBypassCSP",description:"Enable page Content Security Policy by-passing.",experimental:!0,parameters:[{name:"enabled",description:"Whether to bypass page CSP.",type:"boolean"}]},{name:"setDeviceMetricsOverride",description:'Overrides the values of device screen dimensions (window.screen.width, window.screen.height,\nwindow.innerWidth, window.innerHeight, and "device-width"/"device-height"-related CSS media\nquery results).',experimental:!0,deprecated:!0,redirect:"Emulation",parameters:[{name:"width",description:"Overriding width value in pixels (minimum 0, maximum 10000000). 0 disables the override.",type:"integer"},{name:"height",description:"Overriding height value in pixels (minimum 0, maximum 10000000). 0 disables the override.",type:"integer"},{name:"deviceScaleFactor",description:"Overriding device scale factor value. 0 disables the override.",type:"number"},{name:"mobile",description:"Whether to emulate mobile device. This includes viewport meta tag, overlay scrollbars, text\nautosizing and more.",type:"boolean"},{name:"scale",description:"Scale to apply to resulting view image.",optional:!0,type:"number"},{name:"screenWidth",description:"Overriding screen width value in pixels (minimum 0, maximum 10000000).",optional:!0,type:"integer"},{name:"screenHeight",description:"Overriding screen height value in pixels (minimum 0, maximum 10000000).",optional:!0,type:"integer"},{name:"positionX",description:"Overriding view X position on screen in pixels (minimum 0, maximum 10000000).",optional:!0,type:"integer"},{name:"positionY",description:"Overriding view Y position on screen in pixels (minimum 0, maximum 10000000).",optional:!0,type:"integer"},{name:"dontSetVisibleSize",description:"Do not set visible view size, rely upon explicit setVisibleSize call.",optional:!0,type:"boolean"},{name:"screenOrientation",description:"Screen orientation override.",optional:!0,$ref:"Emulation.ScreenOrientation"},{name:"viewport",description:"The viewport dimensions and scale. If not set, the override is cleared.",optional:!0,$ref:"Viewport"}]},{name:"setDeviceOrientationOverride",description:"Overrides the Device Orientation.",experimental:!0,deprecated:!0,redirect:"DeviceOrientation",parameters:[{name:"alpha",description:"Mock alpha",type:"number"},{name:"beta",description:"Mock beta",type:"number"},{name:"gamma",description:"Mock gamma",type:"number"}]},{name:"setFontFamilies",description:"Set generic font families.",experimental:!0,parameters:[{name:"fontFamilies",description:"Specifies font families to set. If a font family is not specified, it won't be changed.",$ref:"FontFamilies"}]},{name:"setFontSizes",description:"Set default font sizes.",experimental:!0,parameters:[{name:"fontSizes",description:"Specifies font sizes to set. If a font size is not specified, it won't be changed.",$ref:"FontSizes"}]},{name:"setDocumentContent",description:"Sets given markup as the document's HTML.",parameters:[{name:"frameId",description:"Frame id to set HTML for.",$ref:"FrameId"},{name:"html",description:"HTML content to set.",type:"string"}]},{name:"setDownloadBehavior",description:"Set the behavior when downloading a file.",experimental:!0,parameters:[{name:"behavior",description:"Whether to allow all or deny all download requests, or use default Chrome behavior if\navailable (otherwise deny).",type:"string",enum:["deny","allow","default"]},{name:"downloadPath",description:"The default path to save downloaded files to. This is requred if behavior is set to 'allow'",optional:!0,type:"string"}]},{name:"setGeolocationOverride",description:"Overrides the Geolocation Position or Error. Omitting any of the parameters emulates position\nunavailable.",deprecated:!0,redirect:"Emulation",parameters:[{name:"latitude",description:"Mock latitude",optional:!0,type:"number"},{name:"longitude",description:"Mock longitude",optional:!0,type:"number"},{name:"accuracy",description:"Mock accuracy",optional:!0,type:"number"}]},{name:"setLifecycleEventsEnabled",description:"Controls whether page will emit lifecycle events.",experimental:!0,parameters:[{name:"enabled",description:"If true, starts emitting lifecycle events.",type:"boolean"}]},{name:"setTouchEmulationEnabled",description:"Toggles mouse event-based touch event emulation.",experimental:!0,deprecated:!0,redirect:"Emulation",parameters:[{name:"enabled",description:"Whether the touch event emulation should be enabled.",type:"boolean"},{name:"configuration",description:"Touch/gesture events configuration. Default: current platform.",optional:!0,type:"string",enum:["mobile","desktop"]}]},{name:"startScreencast",description:"Starts sending each frame using the `screencastFrame` event.",experimental:!0,parameters:[{name:"format",description:"Image compression format.",optional:!0,type:"string",enum:["jpeg","png"]},{name:"quality",description:"Compression quality from range [0..100].",optional:!0,type:"integer"},{name:"maxWidth",description:"Maximum screenshot width.",optional:!0,type:"integer"},{name:"maxHeight",description:"Maximum screenshot height.",optional:!0,type:"integer"},{name:"everyNthFrame",description:"Send every n-th frame.",optional:!0,type:"integer"}]},{name:"stopLoading",description:"Force the page stop all navigations and pending resource fetches."},{name:"crash",description:"Crashes renderer on the IO thread, generates minidumps.",experimental:!0},{name:"close",description:"Tries to close page, running its beforeunload hooks, if any.",experimental:!0},{name:"setWebLifecycleState",description:"Tries to update the web lifecycle state of the page.\nIt will transition the page to the given state according to:\nhttps://github.com/WICG/web-lifecycle/",experimental:!0,parameters:[{name:"state",description:"Target lifecycle state",type:"string",enum:["frozen","active"]}]},{name:"stopScreencast",description:"Stops sending each frame in the `screencastFrame`.",experimental:!0}],events:[{name:"domContentEventFired",parameters:[{name:"timestamp",$ref:"Network.MonotonicTime"}]},{name:"frameAttached",description:"Fired when frame has been attached to its parent.",parameters:[{name:"frameId",description:"Id of the frame that has been attached.",$ref:"FrameId"},{name:"parentFrameId",description:"Parent frame identifier.",$ref:"FrameId"},{name:"stack",description:"JavaScript stack trace of when frame was attached, only set if frame initiated from script.",optional:!0,$ref:"Runtime.StackTrace"}]},{name:"frameClearedScheduledNavigation",description:"Fired when frame no longer has a scheduled navigation.",experimental:!0,parameters:[{name:"frameId",description:"Id of the frame that has cleared its scheduled navigation.",$ref:"FrameId"}]},{name:"frameDetached",description:"Fired when frame has been detached from its parent.",parameters:[{name:"frameId",description:"Id of the frame that has been detached.",$ref:"FrameId"}]},{name:"frameNavigated",description:"Fired once navigation of the frame has completed. Frame is now associated with the new loader.",parameters:[{name:"frame",description:"Frame object.",$ref:"Frame"}]},{name:"frameResized",experimental:!0},{name:"frameScheduledNavigation",description:"Fired when frame schedules a potential navigation.",experimental:!0,parameters:[{name:"frameId",description:"Id of the frame that has scheduled a navigation.",$ref:"FrameId"},{name:"delay",description:"Delay (in seconds) until the navigation is scheduled to begin. The navigation is not\nguaranteed to start.",type:"number"},{name:"reason",description:"The reason for the navigation.",type:"string",enum:["formSubmissionGet","formSubmissionPost","httpHeaderRefresh","scriptInitiated","metaTagRefresh","pageBlockInterstitial","reload"]},{name:"url",description:"The destination URL for the scheduled navigation.",type:"string"}]},{name:"frameStartedLoading",description:"Fired when frame has started loading.",experimental:!0,parameters:[{name:"frameId",description:"Id of the frame that has started loading.",$ref:"FrameId"}]},{name:"frameStoppedLoading",description:"Fired when frame has stopped loading.",experimental:!0,parameters:[{name:"frameId",description:"Id of the frame that has stopped loading.",$ref:"FrameId"}]},{name:"interstitialHidden",description:"Fired when interstitial page was hidden"},{name:"interstitialShown",description:"Fired when interstitial page was shown"},{name:"javascriptDialogClosed",description:"Fired when a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload) has been\nclosed.",parameters:[{name:"result",description:"Whether dialog was confirmed.",type:"boolean"},{name:"userInput",description:"User input in case of prompt.",type:"string"}]},{name:"javascriptDialogOpening",description:"Fired when a JavaScript initiated dialog (alert, confirm, prompt, or onbeforeunload) is about to\nopen.",parameters:[{name:"url",description:"Frame url.",type:"string"},{name:"message",description:"Message that will be displayed by the dialog.",type:"string"},{name:"type",description:"Dialog type.",$ref:"DialogType"},{name:"hasBrowserHandler",description:"True iff browser is capable showing or acting on the given dialog. When browser has no\ndialog handler for given target, calling alert while Page domain is engaged will stall\nthe page execution. Execution can be resumed via calling Page.handleJavaScriptDialog.",type:"boolean"},{name:"defaultPrompt",description:"Default dialog prompt.",optional:!0,type:"string"}]},{name:"lifecycleEvent",description:"Fired for top level page lifecycle events such as navigation, load, paint, etc.",parameters:[{name:"frameId",description:"Id of the frame.",$ref:"FrameId"},{name:"loaderId",description:"Loader identifier. Empty string if the request is fetched from worker.",$ref:"Network.LoaderId"},{name:"name",type:"string"},{name:"timestamp",$ref:"Network.MonotonicTime"}]},{name:"loadEventFired",parameters:[{name:"timestamp",$ref:"Network.MonotonicTime"}]},{name:"navigatedWithinDocument",description:"Fired when same-document navigation happens, e.g. due to history API usage or anchor navigation.",experimental:!0,parameters:[{name:"frameId",description:"Id of the frame.",$ref:"FrameId"},{name:"url",description:"Frame's new url.",type:"string"}]},{name:"screencastFrame",description:"Compressed image data requested by the `startScreencast`.",experimental:!0,parameters:[{name:"data",description:"Base64-encoded compressed image.",type:"string"},{name:"metadata",description:"Screencast frame metadata.",$ref:"ScreencastFrameMetadata"},{name:"sessionId",description:"Frame number.",type:"integer"}]},{name:"screencastVisibilityChanged",description:"Fired when the page with currently enabled screencast was shown or hidden `.",experimental:!0,parameters:[{name:"visible",description:"True if the page is visible.",type:"boolean"}]},{name:"windowOpen",description:"Fired when a new window is going to be opened, via window.open(), link click, form submission,\netc.",parameters:[{name:"url",description:"The URL for the new window.",type:"string"},{name:"windowName",description:"Window name.",type:"string"},{name:"windowFeatures",description:"An array of enabled window features.",type:"array",items:{type:"string"}},{name:"userGesture",description:"Whether or not it was triggered by user gesture.",type:"boolean"}]}]},{domain:"Performance",types:[{id:"Metric",description:"Run-time execution metric.",type:"object",properties:[{name:"name",description:"Metric name.",type:"string"},{name:"value",description:"Metric value.",type:"number"}]}],commands:[{name:"disable",description:"Disable collecting and reporting metrics."},{name:"enable",description:"Enable collecting and reporting metrics."},{name:"getMetrics",
description:"Retrieve current values of run-time metrics.",returns:[{name:"metrics",description:"Current values for run-time metrics.",type:"array",items:{$ref:"Metric"}}]}],events:[{name:"metrics",description:"Current values of the metrics.",parameters:[{name:"metrics",description:"Current values of the metrics.",type:"array",items:{$ref:"Metric"}},{name:"title",description:"Timestamp title.",type:"string"}]}]},{domain:"Security",description:"Security",types:[{id:"CertificateId",description:"An internal certificate ID value.",type:"integer"},{id:"MixedContentType",description:"A description of mixed content (HTTP resources on HTTPS pages), as defined by\nhttps://www.w3.org/TR/mixed-content/#categories",type:"string",enum:["blockable","optionally-blockable","none"]},{id:"SecurityState",description:"The security level of a page or resource.",type:"string",enum:["unknown","neutral","insecure","secure","info"]},{id:"SecurityStateExplanation",description:"An explanation of an factor contributing to the security state.",type:"object",properties:[{name:"securityState",description:"Security state representing the severity of the factor being explained.",$ref:"SecurityState"},{name:"title",description:"Title describing the type of factor.",type:"string"},{name:"summary",description:"Short phrase describing the type of factor.",type:"string"},{name:"description",description:"Full text explanation of the factor.",type:"string"},{name:"mixedContentType",description:"The type of mixed content described by the explanation.",$ref:"MixedContentType"},{name:"certificate",description:"Page certificate.",type:"array",items:{type:"string"}}]},{id:"InsecureContentStatus",description:"Information about insecure content on the page.",type:"object",properties:[{name:"ranMixedContent",description:"True if the page was loaded over HTTPS and ran mixed (HTTP) content such as scripts.",type:"boolean"},{name:"displayedMixedContent",description:"True if the page was loaded over HTTPS and displayed mixed (HTTP) content such as images.",type:"boolean"},{name:"containedMixedForm",description:"True if the page was loaded over HTTPS and contained a form targeting an insecure url.",type:"boolean"},{name:"ranContentWithCertErrors",description:"True if the page was loaded over HTTPS without certificate errors, and ran content such as\nscripts that were loaded with certificate errors.",type:"boolean"},{name:"displayedContentWithCertErrors",description:"True if the page was loaded over HTTPS without certificate errors, and displayed content\nsuch as images that were loaded with certificate errors.",type:"boolean"},{name:"ranInsecureContentStyle",description:"Security state representing a page that ran insecure content.",$ref:"SecurityState"},{name:"displayedInsecureContentStyle",description:"Security state representing a page that displayed insecure content.",$ref:"SecurityState"}]},{id:"CertificateErrorAction",description:"The action to take when a certificate error occurs. continue will continue processing the\nrequest and cancel will cancel the request.",type:"string",enum:["continue","cancel"]}],commands:[{name:"disable",description:"Disables tracking security state changes."},{name:"enable",description:"Enables tracking security state changes."},{name:"setIgnoreCertificateErrors",description:"Enable/disable whether all certificate errors should be ignored.",experimental:!0,parameters:[{name:"ignore",description:"If true, all certificate errors will be ignored.",type:"boolean"}]},{name:"handleCertificateError",description:"Handles a certificate error that fired a certificateError event.",deprecated:!0,parameters:[{name:"eventId",description:"The ID of the event.",type:"integer"},{name:"action",description:"The action to take on the certificate error.",$ref:"CertificateErrorAction"}]},{name:"setOverrideCertificateErrors",description:"Enable/disable overriding certificate errors. If enabled, all certificate error events need to\nbe handled by the DevTools client and should be answered with `handleCertificateError` commands.",deprecated:!0,parameters:[{name:"override",description:"If true, certificate errors will be overridden.",type:"boolean"}]}],events:[{name:"certificateError",description:"There is a certificate error. If overriding certificate errors is enabled, then it should be\nhandled with the `handleCertificateError` command. Note: this event does not fire if the\ncertificate error has been allowed internally. Only one client per target should override\ncertificate errors at the same time.",deprecated:!0,parameters:[{name:"eventId",description:"The ID of the event.",type:"integer"},{name:"errorType",description:"The type of the error.",type:"string"},{name:"requestURL",description:"The url that was requested.",type:"string"}]},{name:"securityStateChanged",description:"The security state of the page changed.",parameters:[{name:"securityState",description:"Security state.",$ref:"SecurityState"},{name:"schemeIsCryptographic",description:"True if the page was loaded over cryptographic transport such as HTTPS.",type:"boolean"},{name:"explanations",description:"List of explanations for the security state. If the overall security state is `insecure` or\n`warning`, at least one corresponding explanation should be included.",type:"array",items:{$ref:"SecurityStateExplanation"}},{name:"insecureContentStatus",description:"Information about insecure content on the page.",$ref:"InsecureContentStatus"},{name:"summary",description:"Overrides user-visible description of the state.",optional:!0,type:"string"}]}]},{domain:"ServiceWorker",experimental:!0,types:[{id:"ServiceWorkerRegistration",description:"ServiceWorker registration.",type:"object",properties:[{name:"registrationId",type:"string"},{name:"scopeURL",type:"string"},{name:"isDeleted",type:"boolean"}]},{id:"ServiceWorkerVersionRunningStatus",type:"string",enum:["stopped","starting","running","stopping"]},{id:"ServiceWorkerVersionStatus",type:"string",enum:["new","installing","installed","activating","activated","redundant"]},{id:"ServiceWorkerVersion",description:"ServiceWorker version.",type:"object",properties:[{name:"versionId",type:"string"},{name:"registrationId",type:"string"},{name:"scriptURL",type:"string"},{name:"runningStatus",$ref:"ServiceWorkerVersionRunningStatus"},{name:"status",$ref:"ServiceWorkerVersionStatus"},{name:"scriptLastModified",description:"The Last-Modified header value of the main script.",optional:!0,type:"number"},{name:"scriptResponseTime",description:"The time at which the response headers of the main script were received from the server.\nFor cached script it is the last time the cache entry was validated.",optional:!0,type:"number"},{name:"controlledClients",optional:!0,type:"array",items:{$ref:"Target.TargetID"}},{name:"targetId",optional:!0,$ref:"Target.TargetID"}]},{id:"ServiceWorkerErrorMessage",description:"ServiceWorker error message.",type:"object",properties:[{name:"errorMessage",type:"string"},{name:"registrationId",type:"string"},{name:"versionId",type:"string"},{name:"sourceURL",type:"string"},{name:"lineNumber",type:"integer"},{name:"columnNumber",type:"integer"}]}],commands:[{name:"deliverPushMessage",parameters:[{name:"origin",type:"string"},{name:"registrationId",type:"string"},{name:"data",type:"string"}]},{name:"disable"},{name:"dispatchSyncEvent",parameters:[{name:"origin",type:"string"},{name:"registrationId",type:"string"},{name:"tag",type:"string"},{name:"lastChance",type:"boolean"}]},{name:"enable"},{name:"inspectWorker",parameters:[{name:"versionId",type:"string"}]},{name:"setForceUpdateOnPageLoad",parameters:[{name:"forceUpdateOnPageLoad",type:"boolean"}]},{name:"skipWaiting",parameters:[{name:"scopeURL",type:"string"}]},{name:"startWorker",parameters:[{name:"scopeURL",type:"string"}]},{name:"stopAllWorkers"},{name:"stopWorker",parameters:[{name:"versionId",type:"string"}]},{name:"unregister",parameters:[{name:"scopeURL",type:"string"}]},{name:"updateRegistration",parameters:[{name:"scopeURL",type:"string"}]}],events:[{name:"workerErrorReported",parameters:[{name:"errorMessage",$ref:"ServiceWorkerErrorMessage"}]},{name:"workerRegistrationUpdated",parameters:[{name:"registrations",type:"array",items:{$ref:"ServiceWorkerRegistration"}}]},{name:"workerVersionUpdated",parameters:[{name:"versions",type:"array",items:{$ref:"ServiceWorkerVersion"}}]}]},{domain:"Storage",experimental:!0,types:[{id:"StorageType",description:"Enum of possible storage types.",type:"string",enum:["appcache","cookies","file_systems","indexeddb","local_storage","shader_cache","websql","service_workers","cache_storage","all","other"]},{id:"UsageForType",description:"Usage for a storage type.",type:"object",properties:[{name:"storageType",description:"Name of storage type.",$ref:"StorageType"},{name:"usage",description:"Storage usage (bytes).",type:"number"}]}],commands:[{name:"clearDataForOrigin",description:"Clears storage for origin.",parameters:[{name:"origin",description:"Security origin.",type:"string"},{name:"storageTypes",description:"Comma separated origin names.",type:"string"}]},{name:"getUsageAndQuota",description:"Returns usage and quota in bytes.",parameters:[{name:"origin",description:"Security origin.",type:"string"}],returns:[{name:"usage",description:"Storage usage (bytes).",type:"number"},{name:"quota",description:"Storage quota (bytes).",type:"number"},{name:"usageBreakdown",description:"Storage usage per type (bytes).",type:"array",items:{$ref:"UsageForType"}}]},{name:"trackCacheStorageForOrigin",description:"Registers origin to be notified when an update occurs to its cache storage list.",parameters:[{name:"origin",description:"Security origin.",type:"string"}]},{name:"trackIndexedDBForOrigin",description:"Registers origin to be notified when an update occurs to its IndexedDB.",parameters:[{name:"origin",description:"Security origin.",type:"string"}]},{name:"untrackCacheStorageForOrigin",description:"Unregisters origin from receiving notifications for cache storage.",parameters:[{name:"origin",description:"Security origin.",type:"string"}]},{name:"untrackIndexedDBForOrigin",description:"Unregisters origin from receiving notifications for IndexedDB.",parameters:[{name:"origin",description:"Security origin.",type:"string"}]}],events:[{name:"cacheStorageContentUpdated",description:"A cache's contents have been modified.",parameters:[{name:"origin",description:"Origin to update.",type:"string"},{name:"cacheName",description:"Name of cache in origin.",type:"string"}]},{name:"cacheStorageListUpdated",description:"A cache has been added/deleted.",parameters:[{name:"origin",description:"Origin to update.",type:"string"}]},{name:"indexedDBContentUpdated",description:"The origin's IndexedDB object store has been modified.",parameters:[{name:"origin",description:"Origin to update.",type:"string"},{name:"databaseName",description:"Database to update.",type:"string"},{name:"objectStoreName",description:"ObjectStore to update.",type:"string"}]},{name:"indexedDBListUpdated",description:"The origin's IndexedDB database list has been modified.",parameters:[{name:"origin",description:"Origin to update.",type:"string"}]}]},{domain:"SystemInfo",description:"The SystemInfo domain defines methods and events for querying low-level system information.",experimental:!0,types:[{id:"GPUDevice",description:"Describes a single graphics processor (GPU).",type:"object",properties:[{name:"vendorId",description:"PCI ID of the GPU vendor, if available; 0 otherwise.",type:"number"},{name:"deviceId",description:"PCI ID of the GPU device, if available; 0 otherwise.",type:"number"},{name:"vendorString",description:"String description of the GPU vendor, if the PCI ID is not available.",type:"string"},{name:"deviceString",description:"String description of the GPU device, if the PCI ID is not available.",type:"string"}]},{id:"GPUInfo",description:"Provides information about the GPU(s) on the system.",type:"object",properties:[{name:"devices",description:"The graphics devices on the system. Element 0 is the primary GPU.",type:"array",items:{$ref:"GPUDevice"}},{name:"auxAttributes",description:"An optional dictionary of additional GPU related attributes.",optional:!0,type:"object"},{name:"featureStatus",description:"An optional dictionary of graphics features and their status.",optional:!0,type:"object"},{name:"driverBugWorkarounds",description:"An optional array of GPU driver bug workarounds.",type:"array",items:{type:"string"}}]}],commands:[{name:"getInfo",description:"Returns information about the system.",returns:[{name:"gpu",description:"Information about the GPUs on the system.",$ref:"GPUInfo"},{name:"modelName",description:"A platform-dependent description of the model of the machine. On Mac OS, this is, for\nexample, 'MacBookPro'. Will be the empty string if not supported.",type:"string"},{name:"modelVersion",description:"A platform-dependent description of the version of the machine. On Mac OS, this is, for\nexample, '10.1'. Will be the empty string if not supported.",type:"string"},{name:"commandLine",description:"The command line string used to launch the browser. Will be the empty string if not\nsupported.",type:"string"}]}]},{domain:"Target",description:"Supports additional targets discovery and allows to attach to them.",types:[{id:"TargetID",type:"string"},{id:"SessionID",description:"Unique identifier of attached debugging session.",type:"string"},{id:"BrowserContextID",experimental:!0,type:"string"},{id:"TargetInfo",type:"object",properties:[{name:"targetId",$ref:"TargetID"},{name:"type",type:"string"},{name:"title",type:"string"},{name:"url",type:"string"},{name:"attached",description:"Whether the target has an attached client.",type:"boolean"},{name:"openerId",description:"Opener target Id",optional:!0,$ref:"TargetID"},{name:"browserContextId",experimental:!0,optional:!0,$ref:"BrowserContextID"}]},{id:"RemoteLocation",experimental:!0,type:"object",properties:[{name:"host",type:"string"},{name:"port",type:"integer"}]}],commands:[{name:"activateTarget",description:"Activates (focuses) the target.",parameters:[{name:"targetId",$ref:"TargetID"}]},{name:"attachToTarget",description:"Attaches to the target with given id.",parameters:[{name:"targetId",$ref:"TargetID"}],returns:[{name:"sessionId",description:"Id assigned to the session.",$ref:"SessionID"}]},{name:"closeTarget",description:"Closes the target. If the target is a page that gets closed too.",parameters:[{name:"targetId",$ref:"TargetID"}],returns:[{name:"success",type:"boolean"}]},{name:"exposeDevToolsProtocol",description:"Inject object to the target's main frame that provides a communication\nchannel with browser target.\n\nInjected object will be available as `window[bindingName]`.\n\nThe object has the follwing API:\n- `binding.send(json)` - a method to send messages over the remote debugging protocol\n- `binding.onmessage = json => handleMessage(json)` - a callback that will be called for the protocol notifications and command responses.",experimental:!0,parameters:[{name:"targetId",$ref:"TargetID"},{name:"bindingName",description:"Binding name, 'cdp' if not specified.",optional:!0,type:"string"}]},{name:"createBrowserContext",description:"Creates a new empty BrowserContext. Similar to an incognito profile but you can have more than\none.",experimental:!0,returns:[{name:"browserContextId",description:"The id of the context created.",$ref:"BrowserContextID"}]},{name:"getBrowserContexts",description:"Returns all browser contexts created with `Target.createBrowserContext` method.",experimental:!0,returns:[{name:"browserContextIds",description:"An array of browser context ids.",type:"array",items:{$ref:"BrowserContextID"}}]},{name:"createTarget",description:"Creates a new page.",parameters:[{name:"url",description:"The initial URL the page will be navigated to.",type:"string"},{name:"width",description:"Frame width in DIP (headless chrome only).",optional:!0,type:"integer"},{name:"height",description:"Frame height in DIP (headless chrome only).",optional:!0,type:"integer"},{name:"browserContextId",description:"The browser context to create the page in.",optional:!0,$ref:"BrowserContextID"},{name:"enableBeginFrameControl",description:"Whether BeginFrames for this target will be controlled via DevTools (headless chrome only,\nnot supported on MacOS yet, false by default).",experimental:!0,optional:!0,type:"boolean"}],returns:[{name:"targetId",description:"The id of the page opened.",$ref:"TargetID"}]},{name:"detachFromTarget",description:"Detaches session with given id.",parameters:[{name:"sessionId",description:"Session to detach.",optional:!0,$ref:"SessionID"},{name:"targetId",description:"Deprecated.",deprecated:!0,optional:!0,$ref:"TargetID"}]},{name:"disposeBrowserContext",description:"Deletes a BrowserContext. All the belonging pages will be closed without calling their\nbeforeunload hooks.",experimental:!0,parameters:[{name:"browserContextId",$ref:"BrowserContextID"}]},{name:"getTargetInfo",description:"Returns information about a target.",experimental:!0,parameters:[{name:"targetId",optional:!0,$ref:"TargetID"}],returns:[{name:"targetInfo",$ref:"TargetInfo"}]},{name:"getTargets",description:"Retrieves a list of available targets.",returns:[{name:"targetInfos",description:"The list of targets.",type:"array",items:{$ref:"TargetInfo"}}]},{name:"sendMessageToTarget",description:"Sends protocol message over session with given id.",parameters:[{name:"message",type:"string"},{name:"sessionId",description:"Identifier of the session.",optional:!0,$ref:"SessionID"},{name:"targetId",description:"Deprecated.",deprecated:!0,optional:!0,$ref:"TargetID"}]},{name:"setAutoAttach",description:"Controls whether to automatically attach to new targets which are considered to be related to\nthis one. When turned on, attaches to all existing related targets as well. When turned off,\nautomatically detaches from all currently attached targets.",experimental:!0,parameters:[{name:"autoAttach",description:"Whether to auto-attach to related targets.",type:"boolean"},{name:"waitForDebuggerOnStart",description:"Whether to pause new targets when attaching to them. Use `Runtime.runIfWaitingForDebugger`\nto run paused targets.",type:"boolean"}]},{name:"setDiscoverTargets",description:"Controls whether to discover available targets and notify via\n`targetCreated/targetInfoChanged/targetDestroyed` events.",parameters:[{name:"discover",description:"Whether to discover available targets.",type:"boolean"}]},{name:"setRemoteLocations",description:"Enables target discovery for the specified locations, when `setDiscoverTargets` was set to\n`true`.",experimental:!0,parameters:[{name:"locations",description:"List of remote locations.",type:"array",items:{$ref:"RemoteLocation"}}]}],events:[{name:"attachedToTarget",description:"Issued when attached to target because of auto-attach or `attachToTarget` command.",experimental:!0,parameters:[{name:"sessionId",description:"Identifier assigned to the session used to send/receive messages.",$ref:"SessionID"},{name:"targetInfo",$ref:"TargetInfo"},{name:"waitingForDebugger",type:"boolean"}]},{name:"detachedFromTarget",description:"Issued when detached from target for any reason (including `detachFromTarget` command). Can be\nissued multiple times per target if multiple sessions have been attached to it.",experimental:!0,parameters:[{name:"sessionId",description:"Detached session identifier.",$ref:"SessionID"},{name:"targetId",description:"Deprecated.",deprecated:!0,optional:!0,$ref:"TargetID"}]},{name:"receivedMessageFromTarget",description:"Notifies about a new protocol message received from the session (as reported in\n`attachedToTarget` event).",parameters:[{name:"sessionId",description:"Identifier of a session which sends a message.",$ref:"SessionID"},{name:"message",type:"string"},{name:"targetId",description:"Deprecated.",deprecated:!0,optional:!0,$ref:"TargetID"}]},{name:"targetCreated",description:"Issued when a possible inspection target is created.",parameters:[{name:"targetInfo",$ref:"TargetInfo"}]},{name:"targetDestroyed",description:"Issued when a target is destroyed.",parameters:[{name:"targetId",$ref:"TargetID"}]},{name:"targetCrashed",description:"Issued when a target has crashed.",parameters:[{name:"targetId",$ref:"TargetID"},{name:"status",description:"Termination status type.",type:"string"},{name:"errorCode",description:"Termination error code.",type:"integer"}]},{name:"targetInfoChanged",description:"Issued when some information about a target has changed. This only happens between\n`targetCreated` and `targetDestroyed`.",parameters:[{name:"targetInfo",$ref:"TargetInfo"}]}]},{domain:"Tethering",description:"The Tethering domain defines methods and events for browser port binding.",experimental:!0,commands:[{name:"bind",description:"Request browser port binding.",parameters:[{name:"port",description:"Port number to bind.",type:"integer"}]},{name:"unbind",description:"Request browser port unbinding.",parameters:[{name:"port",description:"Port number to unbind.",type:"integer"}]}],events:[{name:"accepted",description:"Informs that port was successfully bound and got a specified connection id.",parameters:[{name:"port",description:"Port number that was successfully bound.",type:"integer"},{name:"connectionId",description:"Connection id to be used.",type:"string"}]}]},{domain:"Tracing",experimental:!0,dependencies:["IO"],types:[{id:"MemoryDumpConfig",description:'Configuration for memory dump. Used only when "memory-infra" category is enabled.',type:"object"},{id:"TraceConfig",type:"object",properties:[{name:"recordMode",description:"Controls how the trace buffer stores data.",optional:!0,type:"string",enum:["recordUntilFull","recordContinuously","recordAsMuchAsPossible","echoToConsole"]},{name:"enableSampling",description:"Turns on JavaScript stack sampling.",optional:!0,type:"boolean"},{name:"enableSystrace",description:"Turns on system tracing.",optional:!0,type:"boolean"},{name:"enableArgumentFilter",description:"Turns on argument filter.",optional:!0,type:"boolean"},{name:"includedCategories",description:"Included category filters.",optional:!0,type:"array",items:{type:"string"}},{name:"excludedCategories",description:"Excluded category filters.",optional:!0,type:"array",items:{type:"string"}},{name:"syntheticDelays",description:"Configuration to synthesize the delays in tracing.",optional:!0,type:"array",items:{type:"string"}},{name:"memoryDumpConfig",description:'Configuration for memory dump triggers. Used only when "memory-infra" category is enabled.',optional:!0,$ref:"MemoryDumpConfig"}]},{id:"StreamCompression",description:"Compression type to use for traces returned via streams.",type:"string",enum:["none","gzip"]}],commands:[{name:"end",description:"Stop trace events collection."},{name:"getCategories",description:"Gets supported tracing categories.",returns:[{name:"categories",description:"A list of supported tracing categories.",type:"array",items:{type:"string"}}]},{name:"recordClockSyncMarker",description:"Record a clock sync marker in the trace.",parameters:[{name:"syncId",description:"The ID of this clock sync marker",type:"string"}]},{name:"requestMemoryDump",description:"Request a global memory dump.",returns:[{name:"dumpGuid",description:"GUID of the resulting global memory dump.",type:"string"},{name:"success",description:"True iff the global memory dump succeeded.",type:"boolean"}]},{name:"start",description:"Start trace events collection.",parameters:[{name:"categories",description:"Category/tag filter",deprecated:!0,optional:!0,type:"string"},{name:"options",description:"Tracing options",deprecated:!0,optional:!0,type:"string"},{name:"bufferUsageReportingInterval",description:"If set, the agent will issue bufferUsage events at this interval, specified in milliseconds",optional:!0,type:"number"},{name:"transferMode",description:"Whether to report trace events as series of dataCollected events or to save trace to a\nstream (defaults to `ReportEvents`).",optional:!0,type:"string",enum:["ReportEvents","ReturnAsStream"]},{name:"streamCompression",description:"Compression format to use. This only applies when using `ReturnAsStream`\ntransfer mode (defaults to `none`)",optional:!0,$ref:"StreamCompression"},{name:"traceConfig",optional:!0,$ref:"TraceConfig"}]}],events:[{name:"bufferUsage",parameters:[{name:"percentFull",description:"A number in range [0..1] that indicates the used size of event buffer as a fraction of its\ntotal size.",optional:!0,type:"number"},{name:"eventCount",description:"An approximate number of events in the trace log.",optional:!0,type:"number"},{name:"value",description:"A number in range [0..1] that indicates the used size of event buffer as a fraction of its\ntotal size.",optional:!0,type:"number"}]},{name:"dataCollected",description:"Contains an bucket of collected trace events. When tracing is stopped collected events will be\nsend as a sequence of dataCollected events followed by tracingComplete event.",parameters:[{name:"value",type:"array",items:{type:"object"}}]},{name:"tracingComplete",description:"Signals that tracing is stopped and there is no trace buffers pending flush, all data were\ndelivered via dataCollected events.",parameters:[{name:"stream",description:"A handle of the stream that holds resulting trace data.",optional:!0,$ref:"IO.StreamHandle"},{name:"streamCompression",description:"Compression format of returned stream.",optional:!0,$ref:"StreamCompression"}]}]},{domain:"Console",description:"This domain is deprecated - use Runtime or Log instead.",deprecated:!0,dependencies:["Runtime"],types:[{id:"ConsoleMessage",description:"Console message.",type:"object",properties:[{name:"source",description:"Message source.",type:"string",enum:["xml","javascript","network","console-api","storage","appcache","rendering","security","other","deprecation","worker"]},{name:"level",description:"Message severity.",type:"string",enum:["log","warning","error","debug","info"]},{name:"text",description:"Message text.",type:"string"},{name:"url",description:"URL of the message origin.",optional:!0,type:"string"},{name:"line",description:"Line number in the resource that generated this message (1-based).",optional:!0,type:"integer"},{name:"column",description:"Column number in the resource that generated this message (1-based).",optional:!0,type:"integer"}]}],commands:[{name:"clearMessages",description:"Does nothing."},{name:"disable",description:"Disables console domain, prevents further console messages from being reported to the client."},{name:"enable",description:"Enables console domain, sends the messages collected so far to the client by means of the\n`messageAdded` notification."}],events:[{name:"messageAdded",description:"Issued when new console message is added.",parameters:[{name:"message",description:"Console message that has been added.",$ref:"ConsoleMessage"}]}]},{domain:"Debugger",description:"Debugger domain exposes JavaScript debugging capabilities. It allows setting and removing\nbreakpoints, stepping through execution, exploring stack traces, etc.",dependencies:["Runtime"],types:[{id:"BreakpointId",description:"Breakpoint identifier.",type:"string"},{id:"CallFrameId",description:"Call frame identifier.",type:"string"},{id:"Location",description:"Location in the source code.",type:"object",properties:[{name:"scriptId",description:"Script identifier as reported in the `Debugger.scriptParsed`.",$ref:"Runtime.ScriptId"},{name:"lineNumber",description:"Line number in the script (0-based).",type:"integer"},{name:"columnNumber",description:"Column number in the script (0-based).",optional:!0,type:"integer"}]},{id:"ScriptPosition",description:"Location in the source code.",experimental:!0,type:"object",properties:[{name:"lineNumber",type:"integer"},{name:"columnNumber",type:"integer"}]},{id:"CallFrame",description:"JavaScript call frame. Array of call frames form the call stack.",type:"object",properties:[{name:"callFrameId",description:"Call frame identifier. This identifier is only valid while the virtual machine is paused.",$ref:"CallFrameId"},{name:"functionName",description:"Name of the JavaScript function called on this call frame.",type:"string"},{name:"functionLocation",description:"Location in the source code.",optional:!0,$ref:"Location"},{name:"location",description:"Location in the source code.",$ref:"Location"},{name:"url",description:"JavaScript script name or url.",type:"string"},{name:"scopeChain",description:"Scope chain for this call frame.",type:"array",items:{$ref:"Scope"}},{name:"this",description:"`this` object for this call frame.",$ref:"Runtime.RemoteObject"},{name:"returnValue",description:"The value being returned, if the function is at return point.",optional:!0,$ref:"Runtime.RemoteObject"}]},{id:"Scope",description:"Scope description.",type:"object",properties:[{name:"type",description:"Scope type.",type:"string",enum:["global","local","with","closure","catch","block","script","eval","module"]},{name:"object",description:"Object representing the scope. For `global` and `with` scopes it represents the actual\nobject; for the rest of the scopes, it is artificial transient object enumerating scope\nvariables as its properties.",$ref:"Runtime.RemoteObject"},{name:"name",optional:!0,type:"string"},{name:"startLocation",description:"Location in the source code where scope starts",optional:!0,$ref:"Location"},{name:"endLocation",description:"Location in the source code where scope ends",optional:!0,$ref:"Location"}]},{id:"SearchMatch",description:"Search match for resource.",type:"object",properties:[{name:"lineNumber",description:"Line number in resource content.",type:"number"},{name:"lineContent",description:"Line with match content.",type:"string"}]},{id:"BreakLocation",type:"object",properties:[{name:"scriptId",description:"Script identifier as reported in the `Debugger.scriptParsed`.",$ref:"Runtime.ScriptId"},{name:"lineNumber",description:"Line number in the script (0-based).",type:"integer"},{name:"columnNumber",description:"Column number in the script (0-based).",optional:!0,type:"integer"},{name:"type",optional:!0,type:"string",enum:["debuggerStatement","call","return"]}]}],commands:[{name:"continueToLocation",description:"Continues execution until specific location is reached.",parameters:[{name:"location",description:"Location to continue to.",$ref:"Location"},{name:"targetCallFrames",optional:!0,type:"string",enum:["any","current"]}]},{name:"disable",description:"Disables debugger for given page."},{name:"enable",description:"Enables debugger for the given page. Clients should not assume that the debugging has been\nenabled until the result for this command is received.",returns:[{name:"debuggerId",description:"Unique identifier of the debugger.",experimental:!0,$ref:"Runtime.UniqueDebuggerId"}]},{name:"evaluateOnCallFrame",description:"Evaluates expression on a given call frame.",parameters:[{name:"callFrameId",description:"Call frame identifier to evaluate on.",$ref:"CallFrameId"},{name:"expression",description:"Expression to evaluate.",type:"string"},{name:"objectGroup",description:"String object group name to put result into (allows rapid releasing resulting object handles\nusing `releaseObjectGroup`).",optional:!0,type:"string"},{name:"includeCommandLineAPI",description:"Specifies whether command line API should be available to the evaluated expression, defaults\nto false.",optional:!0,type:"boolean"},{name:"silent",description:"In silent mode exceptions thrown during evaluation are not reported and do not pause\nexecution. Overrides `setPauseOnException` state.",optional:!0,type:"boolean"},{name:"returnByValue",description:"Whether the result is expected to be a JSON object that should be sent by value.",optional:!0,type:"boolean"},{name:"generatePreview",description:"Whether preview should be generated for the result.",experimental:!0,optional:!0,type:"boolean"},{name:"throwOnSideEffect",
description:"Whether to throw an exception if side effect cannot be ruled out during evaluation.",optional:!0,type:"boolean"},{name:"timeout",description:"Terminate execution after timing out (number of milliseconds).",experimental:!0,optional:!0,$ref:"Runtime.TimeDelta"}],returns:[{name:"result",description:"Object wrapper for the evaluation result.",$ref:"Runtime.RemoteObject"},{name:"exceptionDetails",description:"Exception details.",optional:!0,$ref:"Runtime.ExceptionDetails"}]},{name:"getPossibleBreakpoints",description:"Returns possible locations for breakpoint. scriptId in start and end range locations should be\nthe same.",parameters:[{name:"start",description:"Start of range to search possible breakpoint locations in.",$ref:"Location"},{name:"end",description:"End of range to search possible breakpoint locations in (excluding). When not specified, end\nof scripts is used as end of range.",optional:!0,$ref:"Location"},{name:"restrictToFunction",description:"Only consider locations which are in the same (non-nested) function as start.",optional:!0,type:"boolean"}],returns:[{name:"locations",description:"List of the possible breakpoint locations.",type:"array",items:{$ref:"BreakLocation"}}]},{name:"getScriptSource",description:"Returns source for the script with given id.",parameters:[{name:"scriptId",description:"Id of the script to get source for.",$ref:"Runtime.ScriptId"}],returns:[{name:"scriptSource",description:"Script source.",type:"string"}]},{name:"getStackTrace",description:"Returns stack trace with given `stackTraceId`.",experimental:!0,parameters:[{name:"stackTraceId",$ref:"Runtime.StackTraceId"}],returns:[{name:"stackTrace",$ref:"Runtime.StackTrace"}]},{name:"pause",description:"Stops on the next JavaScript statement."},{name:"pauseOnAsyncCall",experimental:!0,parameters:[{name:"parentStackTraceId",description:"Debugger will pause when async call with given stack trace is started.",$ref:"Runtime.StackTraceId"}]},{name:"removeBreakpoint",description:"Removes JavaScript breakpoint.",parameters:[{name:"breakpointId",$ref:"BreakpointId"}]},{name:"restartFrame",description:"Restarts particular call frame from the beginning.",parameters:[{name:"callFrameId",description:"Call frame identifier to evaluate on.",$ref:"CallFrameId"}],returns:[{name:"callFrames",description:"New stack trace.",type:"array",items:{$ref:"CallFrame"}},{name:"asyncStackTrace",description:"Async stack trace, if any.",optional:!0,$ref:"Runtime.StackTrace"},{name:"asyncStackTraceId",description:"Async stack trace, if any.",experimental:!0,optional:!0,$ref:"Runtime.StackTraceId"}]},{name:"resume",description:"Resumes JavaScript execution."},{name:"scheduleStepIntoAsync",description:"This method is deprecated - use Debugger.stepInto with breakOnAsyncCall and\nDebugger.pauseOnAsyncTask instead. Steps into next scheduled async task if any is scheduled\nbefore next pause. Returns success when async task is actually scheduled, returns error if no\ntask were scheduled or another scheduleStepIntoAsync was called.",experimental:!0},{name:"searchInContent",description:"Searches for given string in script content.",parameters:[{name:"scriptId",description:"Id of the script to search in.",$ref:"Runtime.ScriptId"},{name:"query",description:"String to search for.",type:"string"},{name:"caseSensitive",description:"If true, search is case sensitive.",optional:!0,type:"boolean"},{name:"isRegex",description:"If true, treats string parameter as regex.",optional:!0,type:"boolean"}],returns:[{name:"result",description:"List of search matches.",type:"array",items:{$ref:"SearchMatch"}}]},{name:"setAsyncCallStackDepth",description:"Enables or disables async call stacks tracking.",parameters:[{name:"maxDepth",description:"Maximum depth of async call stacks. Setting to `0` will effectively disable collecting async\ncall stacks (default).",type:"integer"}]},{name:"setBlackboxPatterns",description:"Replace previous blackbox patterns with passed ones. Forces backend to skip stepping/pausing in\nscripts with url matching one of the patterns. VM will try to leave blackboxed script by\nperforming 'step in' several times, finally resorting to 'step out' if unsuccessful.",experimental:!0,parameters:[{name:"patterns",description:"Array of regexps that will be used to check script url for blackbox state.",type:"array",items:{type:"string"}}]},{name:"setBlackboxedRanges",description:"Makes backend skip steps in the script in blackboxed ranges. VM will try leave blacklisted\nscripts by performing 'step in' several times, finally resorting to 'step out' if unsuccessful.\nPositions array contains positions where blackbox state is changed. First interval isn't\nblackboxed. Array should be sorted.",experimental:!0,parameters:[{name:"scriptId",description:"Id of the script.",$ref:"Runtime.ScriptId"},{name:"positions",type:"array",items:{$ref:"ScriptPosition"}}]},{name:"setBreakpoint",description:"Sets JavaScript breakpoint at a given location.",parameters:[{name:"location",description:"Location to set breakpoint in.",$ref:"Location"},{name:"condition",description:"Expression to use as a breakpoint condition. When specified, debugger will only stop on the\nbreakpoint if this expression evaluates to true.",optional:!0,type:"string"}],returns:[{name:"breakpointId",description:"Id of the created breakpoint for further reference.",$ref:"BreakpointId"},{name:"actualLocation",description:"Location this breakpoint resolved into.",$ref:"Location"}]},{name:"setBreakpointByUrl",description:"Sets JavaScript breakpoint at given location specified either by URL or URL regex. Once this\ncommand is issued, all existing parsed scripts will have breakpoints resolved and returned in\n`locations` property. Further matching script parsing will result in subsequent\n`breakpointResolved` events issued. This logical breakpoint will survive page reloads.",parameters:[{name:"lineNumber",description:"Line number to set breakpoint at.",type:"integer"},{name:"url",description:"URL of the resources to set breakpoint on.",optional:!0,type:"string"},{name:"urlRegex",description:"Regex pattern for the URLs of the resources to set breakpoints on. Either `url` or\n`urlRegex` must be specified.",optional:!0,type:"string"},{name:"scriptHash",description:"Script hash of the resources to set breakpoint on.",optional:!0,type:"string"},{name:"columnNumber",description:"Offset in the line to set breakpoint at.",optional:!0,type:"integer"},{name:"condition",description:"Expression to use as a breakpoint condition. When specified, debugger will only stop on the\nbreakpoint if this expression evaluates to true.",optional:!0,type:"string"}],returns:[{name:"breakpointId",description:"Id of the created breakpoint for further reference.",$ref:"BreakpointId"},{name:"locations",description:"List of the locations this breakpoint resolved into upon addition.",type:"array",items:{$ref:"Location"}}]},{name:"setBreakpointOnFunctionCall",description:"Sets JavaScript breakpoint before each call to the given function.\nIf another function was created from the same source as a given one,\ncalling it will also trigger the breakpoint.",experimental:!0,parameters:[{name:"objectId",description:"Function object id.",$ref:"Runtime.RemoteObjectId"},{name:"condition",description:"Expression to use as a breakpoint condition. When specified, debugger will\nstop on the breakpoint if this expression evaluates to true.",optional:!0,type:"string"}],returns:[{name:"breakpointId",description:"Id of the created breakpoint for further reference.",$ref:"BreakpointId"}]},{name:"setBreakpointsActive",description:"Activates / deactivates all breakpoints on the page.",parameters:[{name:"active",description:"New value for breakpoints active state.",type:"boolean"}]},{name:"setPauseOnExceptions",description:"Defines pause on exceptions state. Can be set to stop on all exceptions, uncaught exceptions or\nno exceptions. Initial pause on exceptions state is `none`.",parameters:[{name:"state",description:"Pause on exceptions mode.",type:"string",enum:["none","uncaught","all"]}]},{name:"setReturnValue",description:"Changes return value in top frame. Available only at return break position.",experimental:!0,parameters:[{name:"newValue",description:"New return value.",$ref:"Runtime.CallArgument"}]},{name:"setScriptSource",description:"Edits JavaScript source live.",parameters:[{name:"scriptId",description:"Id of the script to edit.",$ref:"Runtime.ScriptId"},{name:"scriptSource",description:"New content of the script.",type:"string"},{name:"dryRun",description:"If true the change will not actually be applied. Dry run may be used to get result\ndescription without actually modifying the code.",optional:!0,type:"boolean"}],returns:[{name:"callFrames",description:"New stack trace in case editing has happened while VM was stopped.",optional:!0,type:"array",items:{$ref:"CallFrame"}},{name:"stackChanged",description:"Whether current call stack  was modified after applying the changes.",optional:!0,type:"boolean"},{name:"asyncStackTrace",description:"Async stack trace, if any.",optional:!0,$ref:"Runtime.StackTrace"},{name:"asyncStackTraceId",description:"Async stack trace, if any.",experimental:!0,optional:!0,$ref:"Runtime.StackTraceId"},{name:"exceptionDetails",description:"Exception details if any.",optional:!0,$ref:"Runtime.ExceptionDetails"}]},{name:"setSkipAllPauses",description:"Makes page not interrupt on any pauses (breakpoint, exception, dom exception etc).",parameters:[{name:"skip",description:"New value for skip pauses state.",type:"boolean"}]},{name:"setVariableValue",description:"Changes value of variable in a callframe. Object-based scopes are not supported and must be\nmutated manually.",parameters:[{name:"scopeNumber",description:"0-based number of scope as was listed in scope chain. Only 'local', 'closure' and 'catch'\nscope types are allowed. Other scopes could be manipulated manually.",type:"integer"},{name:"variableName",description:"Variable name.",type:"string"},{name:"newValue",description:"New variable value.",$ref:"Runtime.CallArgument"},{name:"callFrameId",description:"Id of callframe that holds variable.",$ref:"CallFrameId"}]},{name:"stepInto",description:"Steps into the function call.",parameters:[{name:"breakOnAsyncCall",description:"Debugger will issue additional Debugger.paused notification if any async task is scheduled\nbefore next pause.",experimental:!0,optional:!0,type:"boolean"}]},{name:"stepOut",description:"Steps out of the function call."},{name:"stepOver",description:"Steps over the statement."}],events:[{name:"breakpointResolved",description:"Fired when breakpoint is resolved to an actual script and location.",parameters:[{name:"breakpointId",description:"Breakpoint unique identifier.",$ref:"BreakpointId"},{name:"location",description:"Actual breakpoint location.",$ref:"Location"}]},{name:"paused",description:"Fired when the virtual machine stopped on breakpoint or exception or any other stop criteria.",parameters:[{name:"callFrames",description:"Call stack the virtual machine stopped on.",type:"array",items:{$ref:"CallFrame"}},{name:"reason",description:"Pause reason.",type:"string",enum:["XHR","DOM","EventListener","exception","assert","debugCommand","promiseRejection","OOM","other","ambiguous"]},{name:"data",description:"Object containing break-specific auxiliary properties.",optional:!0,type:"object"},{name:"hitBreakpoints",description:"Hit breakpoints IDs",optional:!0,type:"array",items:{type:"string"}},{name:"asyncStackTrace",description:"Async stack trace, if any.",optional:!0,$ref:"Runtime.StackTrace"},{name:"asyncStackTraceId",description:"Async stack trace, if any.",experimental:!0,optional:!0,$ref:"Runtime.StackTraceId"},{name:"asyncCallStackTraceId",description:"Just scheduled async call will have this stack trace as parent stack during async execution.\nThis field is available only after `Debugger.stepInto` call with `breakOnAsynCall` flag.",experimental:!0,optional:!0,$ref:"Runtime.StackTraceId"}]},{name:"resumed",description:"Fired when the virtual machine resumed execution."},{name:"scriptFailedToParse",description:"Fired when virtual machine fails to parse the script.",parameters:[{name:"scriptId",description:"Identifier of the script parsed.",$ref:"Runtime.ScriptId"},{name:"url",description:"URL or name of the script parsed (if any).",type:"string"},{name:"startLine",description:"Line offset of the script within the resource with given URL (for script tags).",type:"integer"},{name:"startColumn",description:"Column offset of the script within the resource with given URL.",type:"integer"},{name:"endLine",description:"Last line of the script.",type:"integer"},{name:"endColumn",description:"Length of the last line of the script.",type:"integer"},{name:"executionContextId",description:"Specifies script creation context.",$ref:"Runtime.ExecutionContextId"},{name:"hash",description:"Content hash of the script.",type:"string"},{name:"executionContextAuxData",description:"Embedder-specific auxiliary data.",optional:!0,type:"object"},{name:"sourceMapURL",description:"URL of source map associated with script (if any).",optional:!0,type:"string"},{name:"hasSourceURL",description:"True, if this script has sourceURL.",optional:!0,type:"boolean"},{name:"isModule",description:"True, if this script is ES6 module.",optional:!0,type:"boolean"},{name:"length",description:"This script length.",optional:!0,type:"integer"},{name:"stackTrace",description:"JavaScript top stack frame of where the script parsed event was triggered if available.",experimental:!0,optional:!0,$ref:"Runtime.StackTrace"}]},{name:"scriptParsed",description:"Fired when virtual machine parses script. This event is also fired for all known and uncollected\nscripts upon enabling debugger.",parameters:[{name:"scriptId",description:"Identifier of the script parsed.",$ref:"Runtime.ScriptId"},{name:"url",description:"URL or name of the script parsed (if any).",type:"string"},{name:"startLine",description:"Line offset of the script within the resource with given URL (for script tags).",type:"integer"},{name:"startColumn",description:"Column offset of the script within the resource with given URL.",type:"integer"},{name:"endLine",description:"Last line of the script.",type:"integer"},{name:"endColumn",description:"Length of the last line of the script.",type:"integer"},{name:"executionContextId",description:"Specifies script creation context.",$ref:"Runtime.ExecutionContextId"},{name:"hash",description:"Content hash of the script.",type:"string"},{name:"executionContextAuxData",description:"Embedder-specific auxiliary data.",optional:!0,type:"object"},{name:"isLiveEdit",description:"True, if this script is generated as a result of the live edit operation.",experimental:!0,optional:!0,type:"boolean"},{name:"sourceMapURL",description:"URL of source map associated with script (if any).",optional:!0,type:"string"},{name:"hasSourceURL",description:"True, if this script has sourceURL.",optional:!0,type:"boolean"},{name:"isModule",description:"True, if this script is ES6 module.",optional:!0,type:"boolean"},{name:"length",description:"This script length.",optional:!0,type:"integer"},{name:"stackTrace",description:"JavaScript top stack frame of where the script parsed event was triggered if available.",experimental:!0,optional:!0,$ref:"Runtime.StackTrace"}]}]},{domain:"HeapProfiler",experimental:!0,dependencies:["Runtime"],types:[{id:"HeapSnapshotObjectId",description:"Heap snapshot object id.",type:"string"},{id:"SamplingHeapProfileNode",description:"Sampling Heap Profile node. Holds callsite information, allocation statistics and child nodes.",type:"object",properties:[{name:"callFrame",description:"Function location.",$ref:"Runtime.CallFrame"},{name:"selfSize",description:"Allocations size in bytes for the node excluding children.",type:"number"},{name:"children",description:"Child nodes.",type:"array",items:{$ref:"SamplingHeapProfileNode"}}]},{id:"SamplingHeapProfile",description:"Profile.",type:"object",properties:[{name:"head",$ref:"SamplingHeapProfileNode"}]}],commands:[{name:"addInspectedHeapObject",description:"Enables console to refer to the node with given id via $x (see Command Line API for more details\n$x functions).",parameters:[{name:"heapObjectId",description:"Heap snapshot object id to be accessible by means of $x command line API.",$ref:"HeapSnapshotObjectId"}]},{name:"collectGarbage"},{name:"disable"},{name:"enable"},{name:"getHeapObjectId",parameters:[{name:"objectId",description:"Identifier of the object to get heap object id for.",$ref:"Runtime.RemoteObjectId"}],returns:[{name:"heapSnapshotObjectId",description:"Id of the heap snapshot object corresponding to the passed remote object id.",$ref:"HeapSnapshotObjectId"}]},{name:"getObjectByHeapObjectId",parameters:[{name:"objectId",$ref:"HeapSnapshotObjectId"},{name:"objectGroup",description:"Symbolic group name that can be used to release multiple objects.",optional:!0,type:"string"}],returns:[{name:"result",description:"Evaluation result.",$ref:"Runtime.RemoteObject"}]},{name:"getSamplingProfile",returns:[{name:"profile",description:"Return the sampling profile being collected.",$ref:"SamplingHeapProfile"}]},{name:"startSampling",parameters:[{name:"samplingInterval",description:"Average sample interval in bytes. Poisson distribution is used for the intervals. The\ndefault value is 32768 bytes.",optional:!0,type:"number"}]},{name:"startTrackingHeapObjects",parameters:[{name:"trackAllocations",optional:!0,type:"boolean"}]},{name:"stopSampling",returns:[{name:"profile",description:"Recorded sampling heap profile.",$ref:"SamplingHeapProfile"}]},{name:"stopTrackingHeapObjects",parameters:[{name:"reportProgress",description:"If true 'reportHeapSnapshotProgress' events will be generated while snapshot is being taken\nwhen the tracking is stopped.",optional:!0,type:"boolean"}]},{name:"takeHeapSnapshot",parameters:[{name:"reportProgress",description:"If true 'reportHeapSnapshotProgress' events will be generated while snapshot is being taken.",optional:!0,type:"boolean"}]}],events:[{name:"addHeapSnapshotChunk",parameters:[{name:"chunk",type:"string"}]},{name:"heapStatsUpdate",description:"If heap objects tracking has been started then backend may send update for one or more fragments",parameters:[{name:"statsUpdate",description:"An array of triplets. Each triplet describes a fragment. The first integer is the fragment\nindex, the second integer is a total count of objects for the fragment, the third integer is\na total size of the objects for the fragment.",type:"array",items:{type:"integer"}}]},{name:"lastSeenObjectId",description:"If heap objects tracking has been started then backend regularly sends a current value for last\nseen object id and corresponding timestamp. If the were changes in the heap since last event\nthen one or more heapStatsUpdate events will be sent before a new lastSeenObjectId event.",parameters:[{name:"lastSeenObjectId",type:"integer"},{name:"timestamp",type:"number"}]},{name:"reportHeapSnapshotProgress",parameters:[{name:"done",type:"integer"},{name:"total",type:"integer"},{name:"finished",optional:!0,type:"boolean"}]},{name:"resetProfiles"}]},{domain:"Profiler",dependencies:["Runtime","Debugger"],types:[{id:"ProfileNode",description:"Profile node. Holds callsite information, execution statistics and child nodes.",type:"object",properties:[{name:"id",description:"Unique id of the node.",type:"integer"},{name:"callFrame",description:"Function location.",$ref:"Runtime.CallFrame"},{name:"hitCount",description:"Number of samples where this node was on top of the call stack.",optional:!0,type:"integer"},{name:"children",description:"Child node ids.",optional:!0,type:"array",items:{type:"integer"}},{name:"deoptReason",description:"The reason of being not optimized. The function may be deoptimized or marked as don't\noptimize.",optional:!0,type:"string"},{name:"positionTicks",description:"An array of source position ticks.",optional:!0,type:"array",items:{$ref:"PositionTickInfo"}}]},{id:"Profile",description:"Profile.",type:"object",properties:[{name:"nodes",description:"The list of profile nodes. First item is the root node.",type:"array",items:{$ref:"ProfileNode"}},{name:"startTime",description:"Profiling start timestamp in microseconds.",type:"number"},{name:"endTime",description:"Profiling end timestamp in microseconds.",type:"number"},{name:"samples",description:"Ids of samples top nodes.",optional:!0,type:"array",items:{type:"integer"}},{name:"timeDeltas",description:"Time intervals between adjacent samples in microseconds. The first delta is relative to the\nprofile startTime.",optional:!0,type:"array",items:{type:"integer"}}]},{id:"PositionTickInfo",description:"Specifies a number of samples attributed to a certain source position.",type:"object",properties:[{name:"line",description:"Source line number (1-based).",type:"integer"},{name:"ticks",description:"Number of samples attributed to the source line.",type:"integer"}]},{id:"CoverageRange",description:"Coverage data for a source range.",type:"object",properties:[{name:"startOffset",description:"JavaScript script source offset for the range start.",type:"integer"},{name:"endOffset",description:"JavaScript script source offset for the range end.",type:"integer"},{name:"count",description:"Collected execution count of the source range.",type:"integer"}]},{id:"FunctionCoverage",description:"Coverage data for a JavaScript function.",type:"object",properties:[{name:"functionName",description:"JavaScript function name.",type:"string"},{name:"ranges",description:"Source ranges inside the function with coverage data.",type:"array",items:{$ref:"CoverageRange"}},{name:"isBlockCoverage",description:"Whether coverage data for this function has block granularity.",type:"boolean"}]},{id:"ScriptCoverage",description:"Coverage data for a JavaScript script.",type:"object",properties:[{name:"scriptId",description:"JavaScript script id.",$ref:"Runtime.ScriptId"},{name:"url",description:"JavaScript script name or url.",type:"string"},{name:"functions",description:"Functions contained in the script that has coverage data.",type:"array",items:{$ref:"FunctionCoverage"}}]},{id:"TypeObject",description:"Describes a type collected during runtime.",experimental:!0,type:"object",properties:[{name:"name",description:"Name of a type collected with type profiling.",type:"string"}]},{id:"TypeProfileEntry",description:"Source offset and types for a parameter or return value.",experimental:!0,type:"object",properties:[{name:"offset",description:"Source offset of the parameter or end of function for return values.",type:"integer"},{name:"types",description:"The types for this parameter or return value.",type:"array",items:{$ref:"TypeObject"}}]},{id:"ScriptTypeProfile",description:"Type profile data collected during runtime for a JavaScript script.",experimental:!0,type:"object",properties:[{name:"scriptId",description:"JavaScript script id.",$ref:"Runtime.ScriptId"},{name:"url",description:"JavaScript script name or url.",type:"string"},{name:"entries",description:"Type profile entries for parameters and return values of the functions in the script.",type:"array",items:{$ref:"TypeProfileEntry"}}]}],commands:[{name:"disable"},{name:"enable"},{name:"getBestEffortCoverage",description:"Collect coverage data for the current isolate. The coverage data may be incomplete due to\ngarbage collection.",returns:[{name:"result",description:"Coverage data for the current isolate.",type:"array",items:{$ref:"ScriptCoverage"}}]},{name:"setSamplingInterval",description:"Changes CPU profiler sampling interval. Must be called before CPU profiles recording started.",parameters:[{name:"interval",description:"New sampling interval in microseconds.",type:"integer"}]},{name:"start"},{name:"startPreciseCoverage",description:"Enable precise code coverage. Coverage data for JavaScript executed before enabling precise code\ncoverage may be incomplete. Enabling prevents running optimized code and resets execution\ncounters.",parameters:[{name:"callCount",description:"Collect accurate call counts beyond simple 'covered' or 'not covered'.",optional:!0,type:"boolean"},{name:"detailed",description:"Collect block-based coverage.",optional:!0,type:"boolean"}]},{name:"startTypeProfile",description:"Enable type profile.",experimental:!0},{name:"stop",returns:[{name:"profile",description:"Recorded profile.",$ref:"Profile"}]},{name:"stopPreciseCoverage",description:"Disable precise code coverage. Disabling releases unnecessary execution count records and allows\nexecuting optimized code."},{name:"stopTypeProfile",description:"Disable type profile. Disabling releases type profile data collected so far.",experimental:!0},{name:"takePreciseCoverage",description:"Collect coverage data for the current isolate, and resets execution counters. Precise code\ncoverage needs to have started.",returns:[{name:"result",description:"Coverage data for the current isolate.",type:"array",items:{$ref:"ScriptCoverage"}}]},{name:"takeTypeProfile",description:"Collect type profile.",experimental:!0,returns:[{name:"result",description:"Type profile for all scripts since startTypeProfile() was turned on.",type:"array",items:{$ref:"ScriptTypeProfile"}}]}],events:[{name:"consoleProfileFinished",parameters:[{name:"id",type:"string"},{name:"location",description:"Location of console.profileEnd().",$ref:"Debugger.Location"},{name:"profile",$ref:"Profile"},{name:"title",description:"Profile title passed as an argument to console.profile().",optional:!0,type:"string"}]},{name:"consoleProfileStarted",description:"Sent when new profile recording is started using console.profile() call.",parameters:[{name:"id",type:"string"},{name:"location",description:"Location of console.profile().",$ref:"Debugger.Location"},{name:"title",description:"Profile title passed as an argument to console.profile().",optional:!0,type:"string"}]}]},{domain:"Runtime",description:"Runtime domain exposes JavaScript runtime by means of remote evaluation and mirror objects.\nEvaluation results are returned as mirror object that expose object type, string representation\nand unique identifier that can be used for further object reference. Original objects are\nmaintained in memory unless they are either explicitly released or are released along with the\nother objects in their object group.",types:[{id:"ScriptId",description:"Unique script identifier.",type:"string"},{id:"RemoteObjectId",description:"Unique object identifier.",type:"string"},{id:"UnserializableValue",description:"Primitive value which cannot be JSON-stringified. Includes values `-0`, `NaN`, `Infinity`,\n`-Infinity`, and bigint literals.",type:"string"},{id:"RemoteObject",description:"Mirror object referencing original JavaScript object.",type:"object",properties:[{name:"type",description:"Object type.",type:"string",enum:["object","function","undefined","string","number","boolean","symbol","bigint"]},{name:"subtype",description:"Object subtype hint. Specified for `object` type values only.",optional:!0,type:"string",enum:["array","null","node","regexp","date","map","set","weakmap","weakset","iterator","generator","error","proxy","promise","typedarray"]},{name:"className",description:"Object class (constructor) name. Specified for `object` type values only.",optional:!0,type:"string"},{name:"value",description:"Remote object value in case of primitive values or JSON values (if it was requested).",optional:!0,type:"any"},{name:"unserializableValue",description:"Primitive value which can not be JSON-stringified does not have `value`, but gets this\nproperty.",optional:!0,$ref:"UnserializableValue"},{name:"description",description:"String representation of the object.",optional:!0,type:"string"},{name:"objectId",description:"Unique object identifier (for non-primitive values).",optional:!0,$ref:"RemoteObjectId"},{name:"preview",description:"Preview containing abbreviated property values. Specified for `object` type values only.",experimental:!0,optional:!0,$ref:"ObjectPreview"},{name:"customPreview",experimental:!0,optional:!0,$ref:"CustomPreview"}]},{id:"CustomPreview",experimental:!0,type:"object",properties:[{name:"header",type:"string"},{name:"hasBody",type:"boolean"},{name:"formatterObjectId",$ref:"RemoteObjectId"},{name:"bindRemoteObjectFunctionId",$ref:"RemoteObjectId"},{name:"configObjectId",optional:!0,$ref:"RemoteObjectId"}]},{id:"ObjectPreview",description:"Object containing abbreviated remote object value.",experimental:!0,type:"object",properties:[{name:"type",description:"Object type.",type:"string",enum:["object","function","undefined","string","number","boolean","symbol","bigint"]},{name:"subtype",description:"Object subtype hint. Specified for `object` type values only.",optional:!0,type:"string",enum:["array","null","node","regexp","date","map","set","weakmap","weakset","iterator","generator","error"]},{name:"description",description:"String representation of the object.",optional:!0,type:"string"},{name:"overflow",description:"True iff some of the properties or entries of the original object did not fit.",type:"boolean"},{name:"properties",description:"List of the properties.",type:"array",items:{$ref:"PropertyPreview"}},{name:"entries",description:"List of the entries. Specified for `map` and `set` subtype values only.",optional:!0,type:"array",items:{$ref:"EntryPreview"}}]},{id:"PropertyPreview",experimental:!0,type:"object",properties:[{name:"name",description:"Property name.",type:"string"},{name:"type",description:"Object type. Accessor means that the property itself is an accessor property.",type:"string",enum:["object","function","undefined","string","number","boolean","symbol","accessor","bigint"]},{name:"value",description:"User-friendly property value string.",optional:!0,type:"string"},{name:"valuePreview",description:"Nested value preview.",optional:!0,$ref:"ObjectPreview"},{name:"subtype",description:"Object subtype hint. Specified for `object` type values only.",optional:!0,type:"string",enum:["array","null","node","regexp","date","map","set","weakmap","weakset","iterator","generator","error"]}]},{id:"EntryPreview",experimental:!0,type:"object",properties:[{name:"key",description:"Preview of the key. Specified for map-like collection entries.",optional:!0,$ref:"ObjectPreview"},{name:"value",description:"Preview of the value.",$ref:"ObjectPreview"}]},{id:"PropertyDescriptor",description:"Object property descriptor.",type:"object",properties:[{name:"name",description:"Property name or symbol description.",type:"string"},{name:"value",description:"The value associated with the property.",optional:!0,$ref:"RemoteObject"},{name:"writable",description:"True if the value associated with the property may be changed (data descriptors only).",optional:!0,type:"boolean"},{name:"get",description:"A function which serves as a getter for the property, or `undefined` if there is no getter\n(accessor descriptors only).",optional:!0,$ref:"RemoteObject"},{name:"set",description:"A function which serves as a setter for the property, or `undefined` if there is no setter\n(accessor descriptors only).",optional:!0,$ref:"RemoteObject"},{name:"configurable",description:"True if the type of this property descriptor may be changed and if the property may be\ndeleted from the corresponding object.",type:"boolean"},{name:"enumerable",description:"True if this property shows up during enumeration of the properties on the corresponding\nobject.",type:"boolean"},{name:"wasThrown",description:"True if the result was thrown during the evaluation.",optional:!0,type:"boolean"},{name:"isOwn",description:"True if the property is owned for the object.",optional:!0,type:"boolean"},{name:"symbol",description:"Property symbol object, if the property is of the `symbol` type.",optional:!0,$ref:"RemoteObject"}]},{id:"InternalPropertyDescriptor",description:"Object internal property descriptor. This property isn't normally visible in JavaScript code.",
type:"object",properties:[{name:"name",description:"Conventional property name.",type:"string"},{name:"value",description:"The value associated with the property.",optional:!0,$ref:"RemoteObject"}]},{id:"CallArgument",description:"Represents function call argument. Either remote object id `objectId`, primitive `value`,\nunserializable primitive value or neither of (for undefined) them should be specified.",type:"object",properties:[{name:"value",description:"Primitive value or serializable javascript object.",optional:!0,type:"any"},{name:"unserializableValue",description:"Primitive value which can not be JSON-stringified.",optional:!0,$ref:"UnserializableValue"},{name:"objectId",description:"Remote object handle.",optional:!0,$ref:"RemoteObjectId"}]},{id:"ExecutionContextId",description:"Id of an execution context.",type:"integer"},{id:"ExecutionContextDescription",description:"Description of an isolated world.",type:"object",properties:[{name:"id",description:"Unique id of the execution context. It can be used to specify in which execution context\nscript evaluation should be performed.",$ref:"ExecutionContextId"},{name:"origin",description:"Execution context origin.",type:"string"},{name:"name",description:"Human readable name describing given context.",type:"string"},{name:"auxData",description:"Embedder-specific auxiliary data.",optional:!0,type:"object"}]},{id:"ExceptionDetails",description:"Detailed information about exception (or error) that was thrown during script compilation or\nexecution.",type:"object",properties:[{name:"exceptionId",description:"Exception id.",type:"integer"},{name:"text",description:"Exception text, which should be used together with exception object when available.",type:"string"},{name:"lineNumber",description:"Line number of the exception location (0-based).",type:"integer"},{name:"columnNumber",description:"Column number of the exception location (0-based).",type:"integer"},{name:"scriptId",description:"Script ID of the exception location.",optional:!0,$ref:"ScriptId"},{name:"url",description:"URL of the exception location, to be used when the script was not reported.",optional:!0,type:"string"},{name:"stackTrace",description:"JavaScript stack trace if available.",optional:!0,$ref:"StackTrace"},{name:"exception",description:"Exception object if available.",optional:!0,$ref:"RemoteObject"},{name:"executionContextId",description:"Identifier of the context where exception happened.",optional:!0,$ref:"ExecutionContextId"}]},{id:"Timestamp",description:"Number of milliseconds since epoch.",type:"number"},{id:"TimeDelta",description:"Number of milliseconds.",type:"number"},{id:"CallFrame",description:"Stack entry for runtime errors and assertions.",type:"object",properties:[{name:"functionName",description:"JavaScript function name.",type:"string"},{name:"scriptId",description:"JavaScript script id.",$ref:"ScriptId"},{name:"url",description:"JavaScript script name or url.",type:"string"},{name:"lineNumber",description:"JavaScript script line number (0-based).",type:"integer"},{name:"columnNumber",description:"JavaScript script column number (0-based).",type:"integer"}]},{id:"StackTrace",description:"Call frames for assertions or error messages.",type:"object",properties:[{name:"description",description:"String label of this stack trace. For async traces this may be a name of the function that\ninitiated the async call.",optional:!0,type:"string"},{name:"callFrames",description:"JavaScript function name.",type:"array",items:{$ref:"CallFrame"}},{name:"parent",description:"Asynchronous JavaScript stack trace that preceded this stack, if available.",optional:!0,$ref:"StackTrace"},{name:"parentId",description:"Asynchronous JavaScript stack trace that preceded this stack, if available.",experimental:!0,optional:!0,$ref:"StackTraceId"}]},{id:"UniqueDebuggerId",description:"Unique identifier of current debugger.",experimental:!0,type:"string"},{id:"StackTraceId",description:"If `debuggerId` is set stack trace comes from another debugger and can be resolved there. This\nallows to track cross-debugger calls. See `Runtime.StackTrace` and `Debugger.paused` for usages.",experimental:!0,type:"object",properties:[{name:"id",type:"string"},{name:"debuggerId",optional:!0,$ref:"UniqueDebuggerId"}]}],commands:[{name:"awaitPromise",description:"Add handler to promise with given promise object id.",parameters:[{name:"promiseObjectId",description:"Identifier of the promise.",$ref:"RemoteObjectId"},{name:"returnByValue",description:"Whether the result is expected to be a JSON object that should be sent by value.",optional:!0,type:"boolean"},{name:"generatePreview",description:"Whether preview should be generated for the result.",optional:!0,type:"boolean"}],returns:[{name:"result",description:"Promise result. Will contain rejected value if promise was rejected.",$ref:"RemoteObject"},{name:"exceptionDetails",description:"Exception details if stack strace is available.",optional:!0,$ref:"ExceptionDetails"}]},{name:"callFunctionOn",description:"Calls function with given declaration on the given object. Object group of the result is\ninherited from the target object.",parameters:[{name:"functionDeclaration",description:"Declaration of the function to call.",type:"string"},{name:"objectId",description:"Identifier of the object to call function on. Either objectId or executionContextId should\nbe specified.",optional:!0,$ref:"RemoteObjectId"},{name:"arguments",description:"Call arguments. All call arguments must belong to the same JavaScript world as the target\nobject.",optional:!0,type:"array",items:{$ref:"CallArgument"}},{name:"silent",description:"In silent mode exceptions thrown during evaluation are not reported and do not pause\nexecution. Overrides `setPauseOnException` state.",optional:!0,type:"boolean"},{name:"returnByValue",description:"Whether the result is expected to be a JSON object which should be sent by value.",optional:!0,type:"boolean"},{name:"generatePreview",description:"Whether preview should be generated for the result.",experimental:!0,optional:!0,type:"boolean"},{name:"userGesture",description:"Whether execution should be treated as initiated by user in the UI.",optional:!0,type:"boolean"},{name:"awaitPromise",description:"Whether execution should `await` for resulting value and return once awaited promise is\nresolved.",optional:!0,type:"boolean"},{name:"executionContextId",description:"Specifies execution context which global object will be used to call function on. Either\nexecutionContextId or objectId should be specified.",optional:!0,$ref:"ExecutionContextId"},{name:"objectGroup",description:"Symbolic group name that can be used to release multiple objects. If objectGroup is not\nspecified and objectId is, objectGroup will be inherited from object.",optional:!0,type:"string"}],returns:[{name:"result",description:"Call result.",$ref:"RemoteObject"},{name:"exceptionDetails",description:"Exception details.",optional:!0,$ref:"ExceptionDetails"}]},{name:"compileScript",description:"Compiles expression.",parameters:[{name:"expression",description:"Expression to compile.",type:"string"},{name:"sourceURL",description:"Source url to be set for the script.",type:"string"},{name:"persistScript",description:"Specifies whether the compiled script should be persisted.",type:"boolean"},{name:"executionContextId",description:"Specifies in which execution context to perform script run. If the parameter is omitted the\nevaluation will be performed in the context of the inspected page.",optional:!0,$ref:"ExecutionContextId"}],returns:[{name:"scriptId",description:"Id of the script.",optional:!0,$ref:"ScriptId"},{name:"exceptionDetails",description:"Exception details.",optional:!0,$ref:"ExceptionDetails"}]},{name:"disable",description:"Disables reporting of execution contexts creation."},{name:"discardConsoleEntries",description:"Discards collected exceptions and console API calls."},{name:"enable",description:"Enables reporting of execution contexts creation by means of `executionContextCreated` event.\nWhen the reporting gets enabled the event will be sent immediately for each existing execution\ncontext."},{name:"evaluate",description:"Evaluates expression on global object.",parameters:[{name:"expression",description:"Expression to evaluate.",type:"string"},{name:"objectGroup",description:"Symbolic group name that can be used to release multiple objects.",optional:!0,type:"string"},{name:"includeCommandLineAPI",description:"Determines whether Command Line API should be available during the evaluation.",optional:!0,type:"boolean"},{name:"silent",description:"In silent mode exceptions thrown during evaluation are not reported and do not pause\nexecution. Overrides `setPauseOnException` state.",optional:!0,type:"boolean"},{name:"contextId",description:"Specifies in which execution context to perform evaluation. If the parameter is omitted the\nevaluation will be performed in the context of the inspected page.",optional:!0,$ref:"ExecutionContextId"},{name:"returnByValue",description:"Whether the result is expected to be a JSON object that should be sent by value.",optional:!0,type:"boolean"},{name:"generatePreview",description:"Whether preview should be generated for the result.",experimental:!0,optional:!0,type:"boolean"},{name:"userGesture",description:"Whether execution should be treated as initiated by user in the UI.",optional:!0,type:"boolean"},{name:"awaitPromise",description:"Whether execution should `await` for resulting value and return once awaited promise is\nresolved.",optional:!0,type:"boolean"},{name:"throwOnSideEffect",description:"Whether to throw an exception if side effect cannot be ruled out during evaluation.",experimental:!0,optional:!0,type:"boolean"},{name:"timeout",description:"Terminate execution after timing out (number of milliseconds).",experimental:!0,optional:!0,$ref:"TimeDelta"}],returns:[{name:"result",description:"Evaluation result.",$ref:"RemoteObject"},{name:"exceptionDetails",description:"Exception details.",optional:!0,$ref:"ExceptionDetails"}]},{name:"getIsolateId",description:"Returns the isolate id.",experimental:!0,returns:[{name:"id",description:"The isolate id.",type:"string"}]},{name:"getHeapUsage",description:"Returns the JavaScript heap usage.\nIt is the total usage of the corresponding isolate not scoped to a particular Runtime.",experimental:!0,returns:[{name:"usedSize",description:"Used heap size in bytes.",type:"number"},{name:"totalSize",description:"Allocated heap size in bytes.",type:"number"}]},{name:"getProperties",description:"Returns properties of a given object. Object group of the result is inherited from the target\nobject.",parameters:[{name:"objectId",description:"Identifier of the object to return properties for.",$ref:"RemoteObjectId"},{name:"ownProperties",description:"If true, returns properties belonging only to the element itself, not to its prototype\nchain.",optional:!0,type:"boolean"},{name:"accessorPropertiesOnly",description:"If true, returns accessor properties (with getter/setter) only; internal properties are not\nreturned either.",experimental:!0,optional:!0,type:"boolean"},{name:"generatePreview",description:"Whether preview should be generated for the results.",experimental:!0,optional:!0,type:"boolean"}],returns:[{name:"result",description:"Object properties.",type:"array",items:{$ref:"PropertyDescriptor"}},{name:"internalProperties",description:"Internal object properties (only of the element itself).",optional:!0,type:"array",items:{$ref:"InternalPropertyDescriptor"}},{name:"exceptionDetails",description:"Exception details.",optional:!0,$ref:"ExceptionDetails"}]},{name:"globalLexicalScopeNames",description:"Returns all let, const and class variables from global scope.",parameters:[{name:"executionContextId",description:"Specifies in which execution context to lookup global scope variables.",optional:!0,$ref:"ExecutionContextId"}],returns:[{name:"names",type:"array",items:{type:"string"}}]},{name:"queryObjects",parameters:[{name:"prototypeObjectId",description:"Identifier of the prototype to return objects for.",$ref:"RemoteObjectId"},{name:"objectGroup",description:"Symbolic group name that can be used to release the results.",optional:!0,type:"string"}],returns:[{name:"objects",description:"Array with objects.",$ref:"RemoteObject"}]},{name:"releaseObject",description:"Releases remote object with given id.",parameters:[{name:"objectId",description:"Identifier of the object to release.",$ref:"RemoteObjectId"}]},{name:"releaseObjectGroup",description:"Releases all remote objects that belong to a given group.",parameters:[{name:"objectGroup",description:"Symbolic object group name.",type:"string"}]},{name:"runIfWaitingForDebugger",description:"Tells inspected instance to run if it was waiting for debugger to attach."},{name:"runScript",description:"Runs script with given id in a given context.",parameters:[{name:"scriptId",description:"Id of the script to run.",$ref:"ScriptId"},{name:"executionContextId",description:"Specifies in which execution context to perform script run. If the parameter is omitted the\nevaluation will be performed in the context of the inspected page.",optional:!0,$ref:"ExecutionContextId"},{name:"objectGroup",description:"Symbolic group name that can be used to release multiple objects.",optional:!0,type:"string"},{name:"silent",description:"In silent mode exceptions thrown during evaluation are not reported and do not pause\nexecution. Overrides `setPauseOnException` state.",optional:!0,type:"boolean"},{name:"includeCommandLineAPI",description:"Determines whether Command Line API should be available during the evaluation.",optional:!0,type:"boolean"},{name:"returnByValue",description:"Whether the result is expected to be a JSON object which should be sent by value.",optional:!0,type:"boolean"},{name:"generatePreview",description:"Whether preview should be generated for the result.",optional:!0,type:"boolean"},{name:"awaitPromise",description:"Whether execution should `await` for resulting value and return once awaited promise is\nresolved.",optional:!0,type:"boolean"}],returns:[{name:"result",description:"Run result.",$ref:"RemoteObject"},{name:"exceptionDetails",description:"Exception details.",optional:!0,$ref:"ExceptionDetails"}]},{name:"setAsyncCallStackDepth",description:"Enables or disables async call stacks tracking.",redirect:"Debugger",parameters:[{name:"maxDepth",description:"Maximum depth of async call stacks. Setting to `0` will effectively disable collecting async\ncall stacks (default).",type:"integer"}]},{name:"setCustomObjectFormatterEnabled",experimental:!0,parameters:[{name:"enabled",type:"boolean"}]},{name:"setMaxCallStackSizeToCapture",experimental:!0,parameters:[{name:"size",type:"integer"}]},{name:"terminateExecution",description:"Terminate current or next JavaScript execution.\nWill cancel the termination when the outer-most script execution ends.",experimental:!0},{name:"addBinding",description:"If executionContextId is empty, adds binding with the given name on the\nglobal objects of all inspected contexts, including those created later,\nbindings survive reloads.\nIf executionContextId is specified, adds binding only on global object of\ngiven execution context.\nBinding function takes exactly one argument, this argument should be string,\nin case of any other input, function throws an exception.\nEach binding function call produces Runtime.bindingCalled notification.",experimental:!0,parameters:[{name:"name",type:"string"},{name:"executionContextId",optional:!0,$ref:"ExecutionContextId"}]},{name:"removeBinding",description:"This method does not remove binding function from global object but\nunsubscribes current runtime agent from Runtime.bindingCalled notifications.",experimental:!0,parameters:[{name:"name",type:"string"}]}],events:[{name:"bindingCalled",description:"Notification is issued every time when binding is called.",experimental:!0,parameters:[{name:"name",type:"string"},{name:"payload",type:"string"},{name:"executionContextId",description:"Identifier of the context where the call was made.",$ref:"ExecutionContextId"}]},{name:"consoleAPICalled",description:"Issued when console API was called.",parameters:[{name:"type",description:"Type of the call.",type:"string",enum:["log","debug","info","error","warning","dir","dirxml","table","trace","clear","startGroup","startGroupCollapsed","endGroup","assert","profile","profileEnd","count","timeEnd"]},{name:"args",description:"Call arguments.",type:"array",items:{$ref:"RemoteObject"}},{name:"executionContextId",description:"Identifier of the context where the call was made.",$ref:"ExecutionContextId"},{name:"timestamp",description:"Call timestamp.",$ref:"Timestamp"},{name:"stackTrace",description:"Stack trace captured when the call was made.",optional:!0,$ref:"StackTrace"},{name:"context",description:"Console context descriptor for calls on non-default console context (not console.*):\n'anonymous#unique-logger-id' for call on unnamed context, 'name#unique-logger-id' for call\non named context.",experimental:!0,optional:!0,type:"string"}]},{name:"exceptionRevoked",description:"Issued when unhandled exception was revoked.",parameters:[{name:"reason",description:"Reason describing why exception was revoked.",type:"string"},{name:"exceptionId",description:"The id of revoked exception, as reported in `exceptionThrown`.",type:"integer"}]},{name:"exceptionThrown",description:"Issued when exception was thrown and unhandled.",parameters:[{name:"timestamp",description:"Timestamp of the exception.",$ref:"Timestamp"},{name:"exceptionDetails",$ref:"ExceptionDetails"}]},{name:"executionContextCreated",description:"Issued when new execution context is created.",parameters:[{name:"context",description:"A newly created execution context.",$ref:"ExecutionContextDescription"}]},{name:"executionContextDestroyed",description:"Issued when execution context is destroyed.",parameters:[{name:"executionContextId",description:"Id of the destroyed context",$ref:"ExecutionContextId"}]},{name:"executionContextsCleared",description:"Issued when all executionContexts were cleared in browser"},{name:"inspectRequested",description:"Issued when object should be inspected (for example, as a result of inspect() command line API\ncall).",parameters:[{name:"object",$ref:"RemoteObject"},{name:"hints",type:"object"}]}]},{domain:"Schema",description:"This domain is deprecated.",deprecated:!0,types:[{id:"Domain",description:"Description of the protocol domain.",type:"object",properties:[{name:"name",description:"Domain name.",type:"string"},{name:"version",description:"Domain version.",type:"string"}]}],commands:[{name:"getDomains",description:"Returns supported domains.",returns:[{name:"domains",description:"List of supported domains.",type:"array",items:{$ref:"Domain"}}]}]}]}},function(e,t,n){(function(t){"use strict";function i(e){return function(){var t=e.apply(this,arguments);return new Promise(function(e,n){function i(r,o){try{var a=t[r](o),s=a.value}catch(e){return void n(e)}return a.done?void e(s):Promise.resolve(s).then(function(e){i("next",e)},function(e){i("throw",e)})}return i("next")})}}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function a(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},p=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),d=n(2),c=n(45),l=n(33).parse,m=n(48),u=n(49),h=n(41),f=n(3),y=function(e){function t(e,n){r(this,t);var i=n.message;n.data&&(i+=" ("+n.data+")");var a=o(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,i));return a.request=e,a.response=n,a}return a(t,e),t}(Error),g=function(e){function n(e,t){r(this,n);var i=o(this,(n.__proto__||Object.getPrototypeOf(n)).call(this)),a=function(e){var t=void 0,n=e.find(function(e){return!!e.webSocketDebuggerUrl&&(t=t||e,"page"===e.type)});if(n=n||t)return n;throw new Error("No inspectable targets")};return e=e||{},i.host=e.host||h.HOST,i.port=e.port||h.PORT,i.secure=!!e.secure,i.protocol=e.protocol,i.local=!!e.local,i.target=e.target||a,i._notifier=t,i._callbacks={},i._nextCommandId=1,i.webSocketUrl=void 0,i._start(),i}return a(n,e),p(n,[{key:"inspect",value:function(e,t){return t.customInspect=!1,c.inspect(this,t)}},{key:"send",value:function(e,t,n){var i=this;return"function"==typeof t&&(n=t,t=void 0),"function"==typeof n?void this._enqueueCommand(e,t,n):new Promise(function(n,r){i._enqueueCommand(e,t,function(i,o){if(i){var a={method:e,params:t};r(i instanceof Error?i:new y(a,o))}else n(o)})})}},{key:"close",value:function(e){var t=this,n=function(e){t._ws.removeAllListeners("close"),t._ws.close(),t._ws.once("close",function(){t._ws.removeAllListeners(),e()})};return"function"==typeof e?void n(e):new Promise(function(e,t){n(e)})}},{key:"_start",value:function(){function e(){return n.apply(this,arguments)}var n=i(regeneratorRuntime.mark(function e(){var n,i,r,o,a=this;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n={host:this.host,port:this.port,secure:this.secure},e.prev=1,e.next=4,this._fetchDebuggerURL(n);case 4:return i=e.sent,this.webSocketUrl=i,r=l(i),n.host=r.hostname,n.port=r.port||n.port,e.next=11,this._fetchProtocol(n);case 11:return o=e.sent,u.prepare(this,o),e.next=15,this._connectToWebSocket();case 15:t.nextTick(function(){a._notifier.emit("connect",a)}),e.next=21;break;case 18:e.prev=18,e.t0=e.catch(1),this._notifier.emit("error",e.t0);case 21:case"end":return e.stop()}},e,this,[[1,18]])}));return e}()},{key:"_fetchDebuggerURL",value:function(){function e(e){return t.apply(this,arguments)}var t=i(regeneratorRuntime.mark(function e(t){var n,i,r,o,a,p,d,c,l;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=this.target,e.t0="undefined"==typeof n?"undefined":s(n),e.next="string"===e.t0?4:"object"===e.t0?15:"function"===e.t0?17:24;break;case 4:if(i=n,i.startsWith("/")&&(i="ws://"+this.host+":"+this.port+i),!i.match(/^wss?:/i)){e.next=10;break}return e.abrupt("return",i);case 10:return e.next=12,f.List(t);case 12:return r=e.sent,o=r.find(function(e){return e.id===i}),e.abrupt("return",o.webSocketDebuggerUrl);case 15:return a=n,e.abrupt("return",a.webSocketDebuggerUrl);case 17:return p=n,e.next=20,f.List(t);case 20:return d=e.sent,c=p(d),l="number"==typeof c?d[c]:c,e.abrupt("return",l.webSocketDebuggerUrl);case 24:throw new Error('Invalid target argument "'+this.target+'"');case 25:case"end":return e.stop()}},e,this)}));return e}()},{key:"_fetchProtocol",value:function(){function e(e){return t.apply(this,arguments)}var t=i(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.protocol){e.next=4;break}return e.abrupt("return",this.protocol);case 4:return t.local=this.local,e.next=7,f.Protocol(t);case 7:return e.abrupt("return",e.sent);case 8:case"end":return e.stop()}},e,this)}));return e}()},{key:"_connectToWebSocket",value:function(){var e=this;return new Promise(function(t,n){try{e.secure&&(e.webSocketUrl=e.webSocketUrl.replace(/^ws:/i,"wss:")),e._ws=new m(e.webSocketUrl)}catch(e){return void n(e)}e._ws.on("open",function(){t()}),e._ws.on("message",function(t){var n=JSON.parse(t);e._handleMessage(n)}),e._ws.on("close",function(t){e.emit("disconnect")}),e._ws.on("error",function(e){n(e)})})}},{key:"_handleMessage",value:function(e){if(e.id){var t=this._callbacks[e.id];if(!t)return;e.error?t(!0,e.error):t(!1,e.result||{}),delete this._callbacks[e.id],0===Object.keys(this._callbacks).length&&this.emit("ready")}else e.method&&(this.emit("event",e),this.emit(e.method,e.params))}},{key:"_enqueueCommand",value:function(e,t,n){var i=this,r=this._nextCommandId++,o={id:r,method:e,params:t||{}};this._ws.send(JSON.stringify(o),function(e){e?"function"==typeof n&&n(e):i._callbacks[r]=n})}}]),n}(d);e.exports=g}).call(t,n(1))},function(e,t,n){(function(e,i){function r(e,n){var i={seen:[],stylize:a};return arguments.length>=3&&(i.depth=arguments[2]),arguments.length>=4&&(i.colors=arguments[3]),f(n)?i.showHidden=n:n&&t._extend(i,n),S(i.showHidden)&&(i.showHidden=!1),S(i.depth)&&(i.depth=2),S(i.colors)&&(i.colors=!1),S(i.customInspect)&&(i.customInspect=!0),i.colors&&(i.stylize=o),p(i,e,i.depth)}function o(e,t){var n=r.styles[t];return n?"["+r.colors[n][0]+"m"+e+"["+r.colors[n][1]+"m":e}function a(e,t){return e}function s(e){var t={};return e.forEach(function(e,n){t[e]=!0}),t}function p(e,n,i){if(e.customInspect&&n&&R(n.inspect)&&n.inspect!==t.inspect&&(!n.constructor||n.constructor.prototype!==n)){var r=n.inspect(i,e);return v(r)||(r=p(e,r,i)),r}var o=d(e,n);if(o)return o;var a=Object.keys(n),f=s(a);if(e.showHidden&&(a=Object.getOwnPropertyNames(n)),k(n)&&(a.indexOf("message")>=0||a.indexOf("description")>=0))return c(n);if(0===a.length){if(R(n)){var y=n.name?": "+n.name:"";return e.stylize("[Function"+y+"]","special")}if(I(n))return e.stylize(RegExp.prototype.toString.call(n),"regexp");if(T(n))return e.stylize(Date.prototype.toString.call(n),"date");if(k(n))return c(n)}var g="",b=!1,w=["{","}"];if(h(n)&&(b=!0,w=["[","]"]),R(n)){var S=n.name?": "+n.name:"";g=" [Function"+S+"]"}if(I(n)&&(g=" "+RegExp.prototype.toString.call(n)),T(n)&&(g=" "+Date.prototype.toUTCString.call(n)),k(n)&&(g=" "+c(n)),0===a.length&&(!b||0==n.length))return w[0]+g+w[1];if(i<0)return I(n)?e.stylize(RegExp.prototype.toString.call(n),"regexp"):e.stylize("[Object]","special");e.seen.push(n);var x;return x=b?l(e,n,i,f,a):a.map(function(t){return m(e,n,i,f,t,b)}),e.seen.pop(),u(x,g,w)}function d(e,t){if(S(t))return e.stylize("undefined","undefined");if(v(t)){var n="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(n,"string")}return b(t)?e.stylize(""+t,"number"):f(t)?e.stylize(""+t,"boolean"):y(t)?e.stylize("null","null"):void 0}function c(e){return"["+Error.prototype.toString.call(e)+"]"}function l(e,t,n,i,r){for(var o=[],a=0,s=t.length;a<s;++a)j(t,String(a))?o.push(m(e,t,n,i,String(a),!0)):o.push("");return r.forEach(function(r){r.match(/^\d+$/)||o.push(m(e,t,n,i,r,!0))}),o}function m(e,t,n,i,r,o){var a,s,d;if(d=Object.getOwnPropertyDescriptor(t,r)||{value:t[r]},d.get?s=d.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):d.set&&(s=e.stylize("[Setter]","special")),j(i,r)||(a="["+r+"]"),s||(e.seen.indexOf(d.value)<0?(s=y(n)?p(e,d.value,null):p(e,d.value,n-1),s.indexOf("\n")>-1&&(s=o?s.split("\n").map(function(e){return"  "+e}).join("\n").substr(2):"\n"+s.split("\n").map(function(e){return"   "+e}).join("\n"))):s=e.stylize("[Circular]","special")),S(a)){if(o&&r.match(/^\d+$/))return s;a=JSON.stringify(""+r),a.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=e.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),a=e.stylize(a,"string"))}return a+": "+s}function u(e,t,n){var i=0,r=e.reduce(function(e,t){return i++,t.indexOf("\n")>=0&&i++,e+t.replace(/\u001b\[\d\d?m/g,"").length+1},0);return r>60?n[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+n[1]:n[0]+t+" "+e.join(", ")+" "+n[1]}function h(e){return Array.isArray(e)}function f(e){return"boolean"==typeof e}function y(e){return null===e}function g(e){return null==e}function b(e){return"number"==typeof e}function v(e){return"string"==typeof e}function w(e){return"symbol"==typeof e}function S(e){return void 0===e}function I(e){return x(e)&&"[object RegExp]"===$(e)}function x(e){return"object"==typeof e&&null!==e}function T(e){return x(e)&&"[object Date]"===$(e)}function k(e){return x(e)&&("[object Error]"===$(e)||e instanceof Error)}function R(e){return"function"==typeof e}function C(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||"undefined"==typeof e}function $(e){return Object.prototype.toString.call(e)}function D(e){return e<10?"0"+e.toString(10):e.toString(10)}function O(){var e=new Date,t=[D(e.getHours()),D(e.getMinutes()),D(e.getSeconds())].join(":");return[e.getDate(),A[e.getMonth()],t].join(" ")}function j(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var E=/%[sdj%]/g;t.format=function(e){if(!v(e)){for(var t=[],n=0;n<arguments.length;n++)t.push(r(arguments[n]));return t.join(" ")}for(var n=1,i=arguments,o=i.length,a=String(e).replace(E,function(e){if("%%"===e)return"%";if(n>=o)return e;switch(e){case"%s":return String(i[n++]);case"%d":return Number(i[n++]);case"%j":try{return JSON.stringify(i[n++])}catch(e){return"[Circular]"}default:return e}}),s=i[n];n<o;s=i[++n])a+=y(s)||!x(s)?" "+s:" "+r(s);return a},t.deprecate=function(n,r){function o(){if(!a){if(i.throwDeprecation)throw new Error(r);i.traceDeprecation?console.trace(r):console.error(r),a=!0}return n.apply(this,arguments)}if(S(e.process))return function(){return t.deprecate(n,r).apply(this,arguments)};if(i.noDeprecation===!0)return n;var a=!1;return o};var N,P={};t.debuglog=function(e){if(S(N)&&(N=i.env.NODE_DEBUG||""),e=e.toUpperCase(),!P[e])if(new RegExp("\\b"+e+"\\b","i").test(N)){var n=i.pid;P[e]=function(){var i=t.format.apply(t,arguments);console.error("%s %d: %s",e,n,i)}}else P[e]=function(){};return P[e]},t.inspect=r,r.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},r.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},t.isArray=h,t.isBoolean=f,t.isNull=y,t.isNullOrUndefined=g,t.isNumber=b,t.isString=v,t.isSymbol=w,t.isUndefined=S,t.isRegExp=I,t.isObject=x,t.isDate=T,t.isError=k,t.isFunction=R,t.isPrimitive=C,t.isBuffer=n(46);var A=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];t.log=function(){console.log("%s - %s",O(),t.format.apply(t,arguments))},t.inherits=n(47),t._extend=function(e,t){if(!t||!x(t))return e;for(var n=Object.keys(t),i=n.length;i--;)e[n[i]]=t[n[i]];return e}}).call(t,function(){return this}(),n(1))},function(e,t){e.exports=function(e){return e&&"object"==typeof e&&"function"==typeof e.copy&&"function"==typeof e.fill&&"function"==typeof e.readUInt8}},function(e,t){"function"==typeof Object.create?e.exports=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:e.exports=function(e,t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e}},function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,
i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),s=n(2),p=function(e){function t(e){i(this,t);var n=r(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return n._ws=new WebSocket(e),n._ws.onopen=function(){n.emit("open")},n._ws.onclose=function(){n.emit("close")},n._ws.onmessage=function(e){n.emit("message",e.data)},n._ws.onerror=function(){n.emit("error",new Error("WebSocket error"))},n}return o(t,e),a(t,[{key:"close",value:function(){this._ws.close()}},{key:"send",value:function(e,t){try{this._ws.send(e),t()}catch(e){t(e)}}}]),t}(s);e.exports=p},function(e,t){"use strict";function n(e){var t={};return e.forEach(function(e){var n=e.name;delete e.name,t[n]=e}),t}function i(e,t,i){e.category=t,Object.keys(i).forEach(function(r){"name"!==r&&("type"===t&&"properties"===r||"parameters"===r?e[r]=n(i[r]):e[r]=i[r])})}function r(e,t,n){var r=function(i,r){return e.send(t+"."+n.name,i,r)};i(r,"command",n),e[t][n.name]=r}function o(e,t,n){var r=t+"."+n.name,o=function(t){return"function"==typeof t?void e.on(r,t):new Promise(function(t,n){e.once(r,t)})};i(o,"event",n),e[t][n.name]=o}function a(e,t,n){var r={};i(r,"type",n),e[t][n.id]=r}function s(e,t){e.protocol=t,t.domains.forEach(function(t){var n=t.domain;e[n]={},(t.commands||[]).forEach(function(t){r(e,n,t)}),(t.events||[]).forEach(function(t){o(e,n,t)}),(t.types||[]).forEach(function(t){a(e,n,t)})})}e.exports.prepare=s}]);
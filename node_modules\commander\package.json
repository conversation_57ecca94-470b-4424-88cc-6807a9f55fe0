{"name": "commander", "version": "2.11.0", "description": "the complete solution for node.js command-line programs", "keywords": ["commander", "command", "option", "parser"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/commander.js.git"}, "devDependencies": {"should": "^11.2.1", "sinon": "^2.3.5"}, "scripts": {"test": "make test"}, "main": "index", "files": ["index.js"], "dependencies": {}}
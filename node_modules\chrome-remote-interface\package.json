{"name": "chrome-remote-interface", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "contributors": ["<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "description": "Chrome Debugging Protocol interface", "keywords": ["chrome", "debug", "protocol", "remote", "interface"], "homepage": "https://github.com/cyrus-and/chrome-remote-interface", "version": "0.26.1", "repository": {"type": "git", "url": "git://github.com/cyrus-and/chrome-remote-interface.git"}, "bugs": {"url": "http://github.com/cyrus-and/chrome-remote-interface/issues"}, "engine-strict": {"node": ">=8"}, "dependencies": {"commander": "2.11.x", "ws": "^3.3.3"}, "files": ["lib", "bin", "index.js", "chrome-remote-interface.js", "webpack.config.js"], "bin": {"chrome-remote-interface": "./bin/client.js"}, "browser": "chrome-remote-interface.js", "devDependencies": {"babel-core": "^6.26.3", "babel-loader": "^6.2.8", "babel-plugin-transform-async-to-generator": "^6.24.1", "babel-preset-es2015": "^6.18.0", "eslint": "^3.19.0", "json-loader": "^0.5.4", "mocha": "5.x.x", "webpack": "^1.13.3"}, "scripts": {"test": "./scripts/run-tests.sh", "webpack": "webpack", "prepare": "webpack"}}
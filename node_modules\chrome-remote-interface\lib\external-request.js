'use strict';

const dns = require('dns');

const REQUEST_TIMEOUT = 10000;

// callback(err, data)
function externalRequest(transport, options, callback) {
    // perform the DNS lookup manually so that the HTTP host header generated by
    // http.get will contain the IP address, this is needed because since Chrome
    // 66 the host header cannot contain an host name different than localhost
    // (see https://github.com/cyrus-and/chrome-remote-interface/issues/340)
    dns.lookup(options.host, (err, address) => {
        if (err) {
            callback(err);
            return;
        }
        const resolved = Object.assign({}, options);
        resolved.host = address;
        const request = transport.get(resolved, (response) => {
            let data = '';
            response.on('data', (chunk) => {
                data += chunk;
            });
            response.on('end', () => {
                if (response.statusCode === 200) {
                    callback(null, data);
                } else {
                    callback(new Error(data));
                }
            });
        });
        request.setTimeout(REQUEST_TIMEOUT, () => {
            request.abort();
        });
        request.on('error', callback);
    });
}

module.exports = externalRequest;

const username = 'toxl';
const cb = new ChaturbateBrowser();

cb.on('init', async (e) => {
  console.dir(e.settings);
  console.dir(e.chatSettings);
  console.dir(e.initializerSettings);
  console.log(e.csrftoken);
  console.log(e.hasWebsocket);

  console.log(await cb.fetch(`/api/panel/${e.settings.room}/`));
});

cb.on('message', (e) => {
  console.log(e.timestamp);
  console.log(e.method);
  console.dir(e.args);
});

await cb.start();

cb.profile(username);

setTimeout(() => cb.stop(), 10 * 1000);